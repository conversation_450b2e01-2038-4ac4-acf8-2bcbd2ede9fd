# Frontend Development Guidelines for nahui-pv.hds-h5

## 核心技术栈
- **框架:** Vue 3
- **UI 库:** Element Plus
- **状态管理:** Pinia
- **构建工具:** Vite
- **HTTP 客户端:** Axios
- **CSS 预处理器:** Less

## Vue.js 最佳实践
- **使用vue2选项是语法
; - **组合式 API (Composition API):** 对于新组件，优先使用组合式 API 和 `<script setup>` 语法。它提供了更好的代码组织和可复用性。
; - **组件命名:** 组件文件名和注册时使用帕斯卡命名法 (PascalCase)（例如 `MyComponent.vue`）。在模板中使用组件标签时，使用短横线命名法 (kebab-case)（`<my-component>`）。
; - **Props:** 使用明确的类型定义 Props，并在必要时使用验证器。在 `<script setup>` 中使用 `defineProps`。
; - **事件 (Events):** 在 `<script setup>` 中使用 `defineEmits` 声明触发的事件。事件名使用短横线命名法 (kebab-case)（例如 `update:modelValue`）。
; - **响应式系统:** 正确理解和使用 Vue 的响应式系统（例如 `ref`, `reactive`, `computed`, `watch`）。避免不必要的侦听器 (watchers)。
; - **生命周期钩子:** 使用组合式 API 的生命周期钩子（例如 `onMounted`, `onUnmounted`）。

## Pinia 状态管理
- **Store 结构:** 在 `src/stores/modules/` 中定义 Store。每个 Store 应管理特定领域的状态。
- **State:** 保持 State 最小化且可序列化。
- **Getters:** 使用 Getters 获取基于 Store State 的计算/派生状态。
- **Actions:** 使用 Actions 处理异步操作或复杂的同步变更。Actions 可以调用其他的 Actions 或直接修改 State。

## Element Plus 使用规范
- **组件用法:** 遵循 Element Plus 官方文档进行组件使用和 Props 配置。
- **主题化:** 如有需要，使用 Less 变量（可能在 `src/assets/style/` 中）来自定义主题。
- **图标:** 使用 `@element-plus/icons-vue` 提供的图标。

## Axios HTTP 请求
- **实例:** 利用已配置的 Axios 实例（很可能在 `src/axios/http.js` 中）来保持一致的基础 URL、请求头和拦截器。
- **错误处理:** 为 API 请求实现健壮的错误处理，可以利用拦截器。
- **请求/响应结构:** 标准化请求载荷和响应数据的处理方式。

## 样式 (Less)
- **变量:** 对颜色、字体、间距等使用 Less 变量，定义在共享文件中（例如 `src/assets/style/global.less`）。
- **混合 (Mixins):** 为常见的样式模式创建可复用的 Mixins。
- **作用域样式 (Scoped Styles):** 在 Vue 组件中使用 `<style scoped>` 以防止样式泄露，除非确实需要全局样式。
- **结构:** 逻辑地组织 Less 文件（例如，基础样式、组件样式、工具类样式）。

## 代码检查与格式化
- **ESLint:** 严格遵守 `.eslintrc.json` 中定义的 ESLint 规则。定期运行 `npm run lint`。
- **代码风格:** 保持一致的代码格式（缩进、空格、引号等）。

## 通用代码质量
- **模块化:** 将复杂的组件和逻辑分解为更小、可复用的函数和组件。
- **可读性:** 编写清晰、简洁的代码，并在必要时添加注释。使用有意义的变量名、函数名和组件名。
- **性能:** 注意性能影响（例如，避免在模板中进行昂贵的计算，优化循环，适当时使用 `v-memo`）。
- **文件结构:** 遵循现有的项目结构来组织文件（API 调用在 `src/api/`，组件在 `src/components/`，视图在 `src/views/` 等）。
- **无需代码注释:** 除非特别说明，否则在 `src/views/maintainance` 下的业务模块不需要添加注释。
- **注释**: 不添加没有必要的注释，特别是步骤性的注释或者更改的注释