# 生成字典项 SQL 插入语句模板

## 目标表
`rrsjk_light_operation`.`light_operation_dict_item`

## SQL 结构
```sql
INSERT INTO `rrsjk_light_operation`.`light_operation_dict_item`
  (`dict_id`, `dict_code`, `item_text`, `item_value`, `description`, `sort_order`, `status`, `created_by`, `updated_by`, `created_time`, `updated_time`, `del_flag`)
VALUES
  ([dict_id], '[dict_code]', '[item_text_1]', '[item_value_1]', [description_1], [sort_order_1], [status_1], '[created_by_1]', [updated_by_1], '[created_time_1]', [updated_time_1], [del_flag_1]),
  ([dict_id], '[dict_code]', '[item_text_2]', '[item_value_2]', [description_2], [sort_order_2], [status_2], '[created_by_2]', [updated_by_2], '[created_time_2]', [updated_time_2], [del_flag_2]);
-- 根据需要添加更多行
```

## 字段说明

*   `dict_id`: (整数) 字典组的 ID。同一组字典项应具有相同的 `dict_id`。这个 ID 通常对应于 `light_operation_dict` 表中的主键 `id`。
*   `dict_code`: (字符串) 字典类型的编码，例如：`inspection_type`, `station_type`, `organization_type`。同一 `dict_id` 下的所有条目应具有相同的 `dict_code`。
*   `item_text`: (字符串) 字典项显示的文本，例如：`年度巡检`, `总部`。
*   `item_value`: (字符串) 字典项的实际值，通常是英文大写或下划线风格的编码，例如：`ANNUAL`, `head_office`。
*   `description`: (字符串, 可为 NULL) 描述信息。如果无特定描述，则为 `NULL`。
*   `sort_order`: (整数) 排序顺序，决定了字典项在列表或下拉框中的显示顺序。
*   `status`: (整数) 状态，通常为 `1` 表示启用，`0` 表示禁用。
*   `created_by`: (字符串) 创建者，通常为 `'system'` 或记录操作的用户名。
*   `updated_by`: (字符串, 可为 NULL) 更新者。如果无更新，则为 `NULL`。
*   `created_time`: (日期时间字符串) 创建时间，格式：`'YYYY-MM-DD HH:MM:SS'`。
*   `updated_time`: (日期时间字符串, 可为 NULL) 更新时间。如果无更新，则为 `NULL`。
*   `del_flag`: (整数) 删除标记，通常为 `0` 表示未删除，`1` 表示已删除。

## 示例

### 示例 1: 已有字典 (巡检类型 - inspection_type, dict_id=13)
```sql
INSERT INTO `rrsjk_light_operation`.`light_operation_dict_item` (`dict_id`, `dict_code`, `item_text`, `item_value`, `description`, `sort_order`, `status`, `created_by`, `updated_by`, `created_time`, `updated_time`, `del_flag`)
VALUES
  (13, 'inspection_type', '年度巡检', 'ANNUAL', NULL, 1, 1, 'system', NULL, '2025-04-27 09:21:03', NULL, 0),
  (13, 'inspection_type', '临时巡检', 'TEMPORARY', NULL, 2, 1, 'system', NULL, '2025-04-27 09:21:03', NULL, 0);
```

### 示例 2: 根据提供的 Java 常量生成新字典 (组织机构类型 - organization_type)
假设我们有以下Java常量定义：
```java
/**
 * 总部
 */
public static final String HEAD_OFFICE = "head_office";
/**
 * 分公司
 */
public static final String SUB_CENTER = "sub_center";
/**
 * 光伏运维服务商
 */
public static final String LIGHT_OSP = "light_osp";
/**
 * 光伏运维服务兵
 */
public static final String LIGHT_OSP_STAFF = "light_osp_staff";
```

对应的 SQL 插入语句 (假设为此新字典类型分配 `dict_id` 为 15，`dict_code` 为 `organization_type`):
```sql
INSERT INTO `rrsjk_light_operation`.`light_operation_dict_item` (`dict_id`, `dict_code`, `item_text`, `item_value`, `description`, `sort_order`, `status`, `created_by`, `updated_by`, `created_time`, `updated_time`, `del_flag`)
VALUES
  (15, 'organization_type', '总部', 'head_office', NULL, 1, 1, 'system', NULL, '2025-05-21 15:30:00', NULL, 0),
  (15, 'organization_type', '分公司', 'sub_center', NULL, 2, 1, 'system', NULL, '2025-05-21 15:30:00', NULL, 0),
  (15, 'organization_type', '光伏运维服务商', 'light_osp', NULL, 3, 1, 'system', NULL, '2025-05-21 15:30:00', NULL, 0),
  (15, 'organization_type', '光伏运维服务兵', 'light_osp_staff', NULL, 4, 1, 'system', NULL, '2025-05-21 15:30:00', NULL, 0);
```

**重要注意事项:**
*   在实际操作前，请确认 `dict_id` (例如，上述示例中的 `15`) 在 `light_operation_dict` 表中是否已存在或需要新创建。如果 `dict_code` (例如 `organization_type`) 是全新的，您可能还需要在 `light_operation_dict` 表中先定义这个字典类型。
*   `created_time` 和 `updated_time` 应反映实际操作时间，或使用数据库的当前时间函数（如 `NOW()`，具体取决于数据库类型和表设计）。
*   确保所有字符串值都正确使用了单引号包裹。
