{"name": "rrsjk-hds-h5", "private": true, "version": "1.1.0", "type": "module", "scripts": {"dev": "eslint --ext .js,.vue src/ && vite --mode development", "dev-test": "vite --mode production", "dev-build": "vite build --mode development", "build": "vite build --mode production", "lint": "eslint --ext .js,.vue src/"}, "dependencies": {"@vueuse/core": "^13.1.0", "axios": "1.6.0", "bpmn-js": "7.5", "bpmn-js-token-simulation": "^0.10.0", "codemirror-editor-vue3": "2.0.6", "dayjs": "^1.11.13", "diagram-js": "^15.3.0", "diagram-js-minimap": "^2.1.1", "echarts": "^5.4.0", "lodash": "^4.17.21", "min-dash": "^4.2.3", "moment": "^2.29.4", "pinia": "^2.0.23", "print-js": "^1.6.0", "qs": "^6.11.0", "quill": "^2.0.3", "quill-resize-image": "^1.0.7", "vform3-builds": "^3.0.10", "vite-plugin-eslint": "^1.8.1", "vue": "^3.2.37", "vue-router": "^4.4.3", "vue3-treeselect": "^0.1.10", "xml-js": "1.6.11"}, "devDependencies": {"@element-plus/icons-vue": "^2.3.1", "@vitejs/plugin-vue": "^3.0.2", "autoprefixer": "^10.4.20", "element-plus": "^2.7.2", "eslint": "^8.23.1", "eslint-plugin-vue": "^9.5.1", "html2canvas": "^1.4.1", "less": "^4.1.3", "less-loader": "^11.0.0", "vite": "^4.5.0", "vite-plugin-compression": "^0.5.1"}}