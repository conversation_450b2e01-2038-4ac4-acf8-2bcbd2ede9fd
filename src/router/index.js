import {createRouter, createWebHistory} from "vue-router";
import {forLogin} from "@/utils/forLoign";
import Index from "@/layout/Index.vue";
import ospRoutes from "@/router/osp";
import epcRoutes from "@/router/epc";
import pvRoutes from "@/router/pvEnergy";
import eppRoutes from "@/router/epp";
import opsRoutes from "@/router/workManage";
import processManageRoutes from "@/router/processManage";
import sparePartMonitorRoutes from "@/router/sparePartMonitor";
import sparePartsManage from "@/router/sparePartsManage";
import IndexNoMenus from "@/layout/IndexNoMenus.vue";
import baseData from "@/router/baseData";
import sys from "@/router/sys";
import dataBoard from "@/router/dataBoard";
import rent from "@/router/rent";
import appProgram from "@/router/app";
import maintainance from "@/router/maintainance";

const routes = [
    ...appProgram,
    ...ospRoutes,
    ...epcRoutes,
    ...pvRoutes,
    ...eppRoutes,
    ...opsRoutes,
    ...processManageRoutes,
    ...sparePartMonitorRoutes,
    ...baseData,
    ...sparePartsManage,
    ...sys,
    ...dataBoard,
    ...rent,
    ...maintainance,
    {
        path: "/index",
        name: "index",
        redirect: "/",
        component: Index,
        meta: {
            auth: true,
            keepAlive: false,
            title: "",
        },
        children: [
            {
                path: "/",
                name: "home",
                component: () => import("@/views/home.vue"),
                meta: {
                    auth: true,
                    keepAlive: false,
                    title: "首页",
                },
            },
            {
                path: "/stationboard",
                name: "stationboard",
                component: () => import("@/views/stationBoard/householdBoard.vue"),
                meta: {
                    auth: true,
                    keepAlive: false,
                    title: "电站发电收益（户用）",
                },
            },
            {
                path: "/standardData",
                name: "standardData",
                component: () =>
                    import("@/views/reports/valueaddedSharing/standardData.vue"),
                meta: {
                    auth: true,
                    keepAlive: false,
                    title: "人单数据维护",
                },
            },
            {
                path: "/abnormalPowerStation",
                name: "abnormalPowerStation",
                component: () => import("@/views/stationBoard/abnormalPowerStation.vue"),
                meta: {
                    auth: true,
                    keepAlive: false,
                    title: "异常电站报表",
                },
            },
            {
                path: "/companyDimension",
                name: "companyDimension",
                component: () => import("@/views/stationBoard/companyDimension.vue"),
                meta: {
                    auth: true,
                    keepAlive: false,
                    title: "资方-项目公司维度",
                },
            },
            {
                path: "/auditMonitoring",
                name: "auditMonitoring",
                component: () => import("@/views/stationBoard/auditMonitoring.vue"),
                meta: {
                    auth: true,
                    keepAlive: false,
                    title: "人员审核时效报表",
                },
            },
            {
                path: "/schemeReview",
                name: "schemeReview",
                component: () => import("@/views/stationBoard/schemeReview.vue"),
                meta: {
                    auth: true,
                    keepAlive: false,
                    title: "未过审电站闭环报表",
                },
            },
            {
                path: "/ospClosedLoopRate",
                name: "ospClosedLoopRate",
                component: () => import("@/views/stationBoard/ospClosedLoopRate.vue"),
                meta: {
                    auth: true,
                    keepAlive: false,
                    title: "运维报警工单闭环率(户用)",
                },
            }
        ],
    },
    {
        path: "/",
        name: "IndexNoMenus",
        redirect: "/",
        component: IndexNoMenus,
        meta: {
            auth: true,
            keepAlive: false,
            title: "",
        },
        children: [
            {
                path: "/board",
                name: "board",
                component: () => import("@/views/reports/board.vue"),
                meta: {
                    auth: true,
                    keepAlive: false,
                    title: "数据报表",
                },
            },
            {
                path: "/modeRiqing",
                name: "modeRiqing",
                component: () => import("@/views/reports/modeRiqing.vue"),
                meta: {
                    auth: true,
                    keepAlive: false,
                    title: "模式流程日清",
                },
            },
            {
                path: "/stationEarlyWarningBoard",
                name: "stationEarlyWarningBoard",
                component: () =>
                    import("@/views/stationBoard/stationEarlyWarningBoard.vue"),
                meta: {
                    auth: true,
                    keepAlive: false,
                    title: "电站开发预警报表",
                },
            },
            {
                path: "/subCenterAata",
                name: "subCenterAata",
                component: () =>
                    import("@/views/reports/valueaddedSharing/subCenterAata.vue"),
                meta: {
                    auth: true,
                    keepAlive: false,
                    title: "分中心主数据",
                },
            },
            {
                path: "/account",
                name: "account",
                component: () =>
                    import("@/views/reports/valueaddedSharing/account.vue"),
                meta: {
                    auth: true,
                    keepAlive: false,
                    title: "自挣自花账户",
                },
            },
            {
                path: "/electricityRevenue",
                name: "electricityRevenue",
                component: () => import("@/views/stationBoard/electricityRevenue.vue"),
                meta: {
                    auth: true,
                    keepAlive: false,
                    title: "电费收益报表",
                },
            },
            {
                path: "/deliverPowerStation",
                name: "deliverPowerStation",
                component: () => import("@/views/stationBoard/deliverPowerStation.vue"),
                meta: {
                    auth: true,
                    keepAlive: false,
                    title: "交付电站保发分析",
                },
            },
            {
                path: "/deliverPowerStation/deliverPowerStationDetail",
                name: "deliverPowerStationDetail",
                component: () => import("@/views/stationBoard/deliverPowerStationDetail.vue"),
                meta: {
                    auth: true,
                    keepAlive: false,
                    title: "交付电站保发分析明细",
                },
            },
            {
                path: "/networkOperations",
                name: "networkOperations",
                component: () => import("@/views/stationBoard/networkOperations.vue"),
                meta: {
                    auth: true,
                    keepAlive: false,
                    title: "分中心网络建设",
                },
            },
            {
                path: "/operatorReport",
                name: "operatorReport",
                component: () => import("@/views/stationBoard/operatorReport.vue"),
                meta: {
                    auth: true,
                    keepAlive: false,
                    title: "运营商日清",
                },
            },
        ],
    },
    {
        path: "/login",
        name: "login",
        component: () => import("@/views/login.vue"),
        meta: {
            auth: false,
            keepAlive: false,
            title: "登录",
        },
    },
    {
        path: "/404",
        name: "not-found",
        component: () => import("@/views/404.vue"),
        meta: {
            auth: false,
            keepAlive: false,
            title: "not-found",
        },
    },
    {
        path: "/:pathMatch(.*)*",
        redirect: "/404",
        meta: {
            auth: false,
            keepAlive: false,
            title: "not-found",
        },
    },
];

const router = createRouter({
    history: createWebHistory("/h5/"),
    routes,
});

// 登录拦截跳转
router.beforeEach((to, from, next) => {
    // * console.log(to)
    forLogin(to, from, next);
});

export default router;
