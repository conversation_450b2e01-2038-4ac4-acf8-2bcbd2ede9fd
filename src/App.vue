<script setup>
	import Skeleton from '@/components/skeleton.vue'
    import {onBeforeUnmount, onMounted} from "vue";
    import { useTokenStore } from '@/stores/modules/token'
    const store = useTokenStore()

    // * 监听浏览器的visibilitychange事件，当页面从隐藏变为可见时检查Token
    const visibilityHandler = () => {
        document.addEventListener('visibilitychange', () => {
            if (document.visibilityState === 'visible') {
                const currentToken = JSON.parse(window.localStorage.getItem("logins"))?.access_token || '';
                store.onTokenChange(currentToken)
            }
        });
    }

    onMounted(() => {
        document.addEventListener('visibilitychange', visibilityHandler)
    })

    onBeforeUnmount(()=> {
        document.removeEventListener('visibilitychange', visibilityHandler)
    })
</script>

<template>
	<router-view v-slot="{ Component }">
		<Skeleton v-if="!$LOADED"/>
		<component :is="Component" />
	</router-view>
</template>

<style lang="less">
* {
	box-sizing: border-box;
	margin: 0;
	padding: 0;
}

html {
	line-height: 1.5;
}

#app {
	font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, 'Noto Sans', sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol',
		'Noto Color Emoji';
	-webkit-font-smoothing: antialiased;
	-moz-osx-font-smoothing: grayscale;
	color: @base-font-color;
	font-size: 15px;
}

input:-webkit-autofill {
	-webkit-box-shadow: 0 0 999px 999px white inset;
}
</style>
