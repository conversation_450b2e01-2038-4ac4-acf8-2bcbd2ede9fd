/*
 * @Author: Lufy
 * @Date: 2022-08-22 11:41:20
 * @LastEditTime: 2022-08-22 11:45:51
 * @Description: 登录权限跳转
 */
import API from '@/api';
import {
	ElMessage
} from "element-plus"
import {
	useStore
} from '@/stores';
import { isNewVersion } from '@/utils/versionUpdate';

/***
 * @description: 根据菜单权限递归
 * @param {string | Arrary}  params, key
 * @return {Boolean}  true/false
 * @author: Lufy
 */
let menuDeep = (params, key) => {
	return params?.some(item => {
		if (item.url) {
			if (key.includes(item.url)) {
				return true
			}
		} else if (item.children instanceof Array) {
			return menuDeep(item.children, key)
		}
	})
}

// 菜单
let getMenus = (store) => {
	return new Promise((resolve, reject) => {
		API.menus().then(res => {
				store.setArr('menus', res.error? null : res)
				resolve(res)
			})
			.catch(err => {
				reject(err);
			});
	})
}

// 组件权限
let getPermission = (store) => {
	return new Promise((resolve, reject) => {
		API.permissions().then(res => {
				store.setArr('permissions', res.permissions)
				resolve(res.permissions)
			})
			.catch(err => {
				reject(err);
			});
	})
}

/***
 * @description: 路由权限跳转
 * @param {string | Arrary}  token, menus，permission
 * @return {Boolean}  true/false
 * @author: Lufy
 */
export const forLogin = async (to, _from, next) => {
	isNewVersion(to)
	const token = JSON.parse(window.localStorage.getItem("logins"))
	const auth = to.meta.auth
	let isHttp = document.location.protocol;
	let hostname = document.location.hostname;
    // if (isHttp === "http:" && process.env.NODE_ENV === "production" && hostname !== "127.0.0.1") {
    //     // 判断 http 自动转成 https:
    //     let url = window.location.href;
    //     url = url.replace("http:", "https:");
    //     window.location.href = url;
    // }
	if (auth) {
		if (!token) {
			window.localStorage.setItem("keypath", to.path)
			next("/login")
			return
		}
		// 判断是否有菜单路由权限
		const store = useStore()
		const key = to.fullPath
		const menus = store.menus ?? await getMenus(store)
		const permission = store.permissions ?? await getPermission(store)
		if (key === "/" || key === "/index") {
			next()
		} else {
			const allow = menuDeep(menus, to.fullPath)
			if (!allow) {
				next("/")
				ElMessage.warning("您无权限打开此页面")
			} else {
				// 路由嵌入动态接口权限
				// At: 2024/09/26 解决this.$router.push跳转页面后limits无法加载问题
				const routerKey = to.fullPath.split("/").pop().split("?").shift()
				const arr = permission.filter(e => e.includes(':' + routerKey + ':'))
				to.meta.limits = arr
				next()
			}
		}
	} else {
		next()
	}
}

/***
 * @description: 判断是否已经登录
 * @param {String}  token
 * @return {Boolean}  true/false
 * @author: Lufy
 */
export const isLogin = () => {
	const token = JSON.parse(window.localStorage.getItem("logins"))?.access_token
	return token ? true : false
}
