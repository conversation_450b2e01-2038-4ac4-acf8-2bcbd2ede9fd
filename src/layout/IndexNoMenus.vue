<template>
	<el-container class="common-layout">
		<div class="layout-rcont">
			<el-header class="layout-header"><Header /></el-header>
			<el-main class="layout-main">
				<router-view v-slot="{ Component }">
					<keep-alive v-if="$route.meta.keepAlive"><component :is="Component" /></keep-alive>
					<component v-if="!$route.meta.keepAlive" :is="Component" />
				</router-view>
			</el-main>
		</div>
	</el-container>
</template>

<script setup>
import Header from '@/layout/Header.vue';
</script>

<style scoped lang="less">
.common-layout {
	display: flex;
	margin: 0 auto;
	background: @base-background-color;

	.layout-rcont {
		display: flex;
		flex-direction: column;
		width: 100%;
		min-width: 0;
		
		.layout-header {
			position: sticky;
			top: 0;
			left: 0;
			width: 100%;
			padding: 0;
			background: @layout-background-color;
			-webkit-box-shadow: 0 1px 4px rgb(0 21 41 / 8%);
			box-shadow: 0 1px 4px rgb(0 21 41 / 8%);
			z-index: 888;
			
			.header {
				padding-left: 0;
			}
		}

		.layout-main {
			display: flex;
		}
	}
}
</style>
