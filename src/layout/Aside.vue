<template>
  <div class="flex-column-start">
    <div class="logo" @click="toHome()">
      {{ isCollapse ? $SHORTNAME : $BASENAME }}
    </div>
    <el-icon class="collapse" @click="isCollapse = !isCollapse">
      <Fold v-if="!isCollapse" /><Expand v-else />
    </el-icon>

    <el-menu
      class="menus"
      @select="handleMenuSelect"
      v-loading="loading"
      
      :default-active="activeInx"
      :collapse="isCollapse"
      :collapse-transition="false"
      :style="isCollapse ? 'min-width: 0' : ''"
    >
      <el-sub-menu
        v-for="(item, index) in menuArr"
        :key="index"
        :index="String(index)"
      >
        <template #title>
          <el-icon><Menu /></el-icon>
          <span>{{ item.name }}</span>
        </template>
        <el-menu-item
          v-for="(itm, inx) in item.children"
          :key="inx"
          :index="itm.url"
        >
          {{ itm.name }}
        </el-menu-item>
      </el-sub-menu>
    </el-menu>
  </div>
</template>

<script type="text/javascript">
import { useStore } from "@/stores";

export default {
  name: "Asider",
  data() {
    return {
      menuArr: [],
      loading: true,
      activeInx: "",
      isCollapse: false,
      menu: [
        {
          url: "",
          parentId: 1,
          name: "流程管理",
          level: 1,
          children: [
            { name: "流程分类", level: 2, url: "/processManage/category" },
            { name: "表单配置", level: 2, url: "/processManage/form" },
            { name: "流程模型", level: 2, url: "/processManage/model" },
            { name: "部署管理", level: 2, url: "/processManage/deploy" },
          ],
        },
        {
          url: "",
          parentId: 0,
          name: "工作管理",
          level: 1,
          children: [
            { name: "新建流程", level: 2, url: "/workManage/newProcess" },
            { name: "我的流程", level: 2, url: "/workManage/myProcess" },
            { name: "待办任务", level: 2, url: "/workManage/todo" },
            { name: "已办任务", level: 2, url: "/workManage/finished" },
          ],
        },
        {
          url: "",
          parentId: 0,
          name: "基础数据",
          level: 1,
          children: [
            { name: "组织架构", level: 2, url: "/baseData/organizationChart" },
            { name: "仓库管理", level: 2, url: "/baseData/warehouseManage" },
            { name: "备件属性", level: 2, url: "/baseData/partProp" },
            { name: "备件保修期", level: 2, url: "/baseData/partWarranty" },
            { name: "备件替换关系", level: 2, url: "/baseData/partReplace" },
            { name: "调拨圈管理", level: 2, url: "/baseData/dialRing" },
            { name: "字典管理", level: 2, url: "/baseData/dictManage" },
            { name: "库位管理", level: 2, url: "/baseData/inventoryManage" },
          ],
        },
        {
          url: "",
          parentId: 3,
          name: "备件监控",
          level: 1,
          children: [
            {
              name: "工单备件监控",
              level: 2,
              url: "/sparePartMonitor/sparemonitor",
            },
            {
              name: "订单监控",
              level: 2,
              url: "/sparePartMonitor/ordermonitor",
            },
            {
              name: "发货监控",
              level: 2,
              url: "/sparePartMonitor/sendgoodsmonitor",
            },
            {
              name: "退返监控",
              level: 2,
              url: "/sparePartMonitor/returnmonitor",
            },
            {
              name: "现有库存查看",
              level: 2,
              url: "/sparePartMonitor/currentRepertoryView",
            },
            {
              name: "服务商账单流水",
              level: 2,
              url: "/sparePartMonitor/billFlowQuery",
            },
            {
              name: "借件审核",
              level: 2,
              url: "/sparePartMonitor/lendOrderAudit",
            },
          ],
        },
        {
          url: "",
          parentId: 0,
          name: "备件管理",
          level: 1,
          children: [
            {
              name: "储备库调拨订单",
              level: 2,
              url: "/sparePartsManage/transferOrder",
            },
            {
              name: "中心仓入库",
              level: 2,
              url: "/sparePartsManage/centralWarehouseStorage",
            },
          ],
        },
      ],
    };
  },

  // 监听路由变化，菜单重新选中
  watch: {
    $route: {
      handler(newRoute, oldRoute) {
        this.getMenus();
      },
      deep: true, // 深度监听，确保query参数的变化也能触发回调
    },
  },

  mounted() {
    this.getMenus();
  },

  methods: {
    handleMenuSelect(index) {
      let targetItem = null;
      for (const menuGroup of this.menuArr) {
        const found = menuGroup.children.find((child) => child.url === index);
        if (found) {
          targetItem = found;
          break;
        }
      }
      if (!targetItem) return;
      const isHttpUrl = /^https?:\/\//.test(targetItem.url);
      if (isHttpUrl) {
        // 打开外部链接
        // window.open(targetItem.url, "_blank", "noopener,noreferrer");
        // // this.$router.push('/')
        // window.location.href = "/";
        // 弹出确认框，提示即将打开外部链接
        this.$confirm("您即将离开此页面，是否继续？", "提示", {
          confirmButtonText: "继续",
          cancelButtonText: "取消",
          type: "warning",
          closeOnClickModal: false,
          closeOnPressEscape: false,
        })
          .then(() => {
            // 用户点击继续，打开新标签页并刷新当前页跳转首页
            window.open(targetItem.url, "_blank", "noopener,noreferrer");
            window.location.href = "/";
          })
          .catch(() => {
            // 用户点击取消，返回上一级路由
            // this.$router.back();
            window.location.href = "/";
          });
      } else {
        // this.getMenus();
        this.$router.push(targetItem.url);
      }
    },
    toHome() {
      window.location.href = "/";
    },

    getMenus() {
      const store = useStore();
      this.menuArr = store.menus;
      !!this.menuArr &&
        this.menuArr.some((item) => {
          return item.children.some((e) => {
            if (this.$route.fullPath.includes(e.url)) {
              this.activeInx = e.url;
              return true;
            }
          });
        });
      // this.menuArr.push(...this.menu)
      if(!this.menuArr.find(item => item.id === 4001))
			this.menuArr.push(
				...[
              {
									"id": 4003,
									"name": "监控大屏",
									"level": 1,
									"url": "",
									"children": [
											{
													"id": 400301,
													"name": "监控大屏",
													"level": 2,
													"url": "http://operation.xiaoxianglink.com/ospboard/",
													"parentId": 4003,
													"children": null
											},
									]
							},
							{
									"id": 4002,
									"name": "方案库",
									"level": 1,
									"url": "",
									"children": [
											{
													"id": 400201,
													"name": "方案列表",
													"level": 2,
													"url": "/maintainance/solution/index",
													"parentId": 4002,
													"children": null
											},
									]
							},
							{
									"id": 4004,
									"name": "故障库",
									"level": 1,
									"url": "",
									"children": [
											{
													"id": 400401,
													"name": "故障列表",
													"level": 2,
													"url": "/maintainance/faultCategory/list",
													"parentId": 4004,
													"children": null
											},
									]
							},
							{
									"id": 4005,
									"name": "基础资源配置",
									"level": 1,
									"url": "",
									"children": [
											{
													"id": 400501,
													"name": "巡检基础配置",
													"level": 2,
													"url": "/maintainance/config/inspection",
													"parentId": 4005,
													"children": null
											},
											{
													"id": 400502,
													"name": "工单基础配置",
													"level": 2,
													"url": "/maintainance/config/workOrder",
													"parentId": 4005,
													"children": null
											},
											{
													"id": 400503,
													"name": "报表基础配置",
													"level": 2,
													"url": "/maintainance/config/report",
													"parentId": 4005,
													"children": null
											},
									]
							},
              {
									"id": 4001,
									"name": "工单大厅",
									"level": 1,
									"url": "",
									"children": [
                      {
													"id": 400102,
													"name": "运维工单",
													"level": 2,
													"url": "/maintainance/faultWorkOrder/list/index",
													"parentId": 40001,
													"children": null
											},
											// {
											// 		"id": 400101,
											// 		"name": "延期审批",
											// 		"level": 2,
											// 		"url": "/maintainance/faultWorkOrder/list/audit",
											// 		"parentId": 40001,
											// 		"children": null
											// },
											{
													"id": 400103,
													"name": "提报工单",
													"level": 2,
													"url": "/maintainance/faultWorkOrder/list/my",
													"parentId": 40001,
													"children": null
											},
											{
													"id": 400104,
													"name": "工单效率",
													"level": 2,
													"url": "/maintainance/faultWorkOrder/efficient",
													"parentId": 40001,
													"children": null
											},
									]
							},
              {
									"id": 4007,
									"name": "巡检管理",
									"level": 1,
									"url": "",
									"children": [
                      {
													"id": 400701,
													"name": "巡检计划",
													"level": 2,
													"url": "/maintainance/inspect/plan/list",
													"parentId": 40001,
													"children": null
											},
											{
													"id": 400702,
													"name": "巡检任务",
													"level": 2,
													"url": "/maintainance/inspect/task/list",
													"parentId": 40001,
													"children": null
											},
									]
							},
							{
									"id": 4006,
									"name": "电站中心",
									"level": 1,
									"url": "",
									"children": [
											{
													"id": 400601,
													"name": "电站列表",
													"level": 2,
													"url": "/maintainance/station/list",
													"parentId": 4006,
													"children": null
											},
									]
							},
							{
									"id": 4008,
									"name": "电费管理",
									"level": 1,
									"url": "",
									"children": [
											{
													"id": 400801,
													"name": "电费列表",
													"level": 2,
													"url": "/maintainance/elecBill/index",
													"parentId": 4008,
													"children": null
											},
											{
													"id": 400802,
													"name": "电费编辑",
													"level": 2,
													"url": "/maintainance/elecBill/edit",
													"parentId": 4008,
													"children": null
											},
									]
							},
							{
									"id": 4009,
									"name": "报表大厅",
									"level": 1,
									"url": "",
									"children": [
											{
													"id": 400901,
													"name": "报表管理",
													"level": 2,
													"url": "/maintainance/report/index",
													"parentId": 4009,
													"children": null
											},
									]
							},
							{
									"id": 4010,
									"name": "消息管理",
									"level": 1,
									"url": "",
									"children": [
											{
													"id": 401001,
													"name": "消息管理",
													"level": 2,
													"url": "/maintainance/message/index",
													"parentId": 4010,
													"children": null
											},
											{
													"id": 401002,
													"name": "发送消息",
													"level": 2,
													"url": "/maintainance/message/send",
													"parentId": 4010,
													"children": null
											},
									]
							},
							{
									"id": 4011,
									"name": "培训中心",
									"level": 1,
									"url": "",
									"children": [
											{
													"id": 401101,
													"name": "分类管理",
													"level": 2,
													"url": "/maintainance/training/category/index",
													"parentId": 4011,
													"children": null
											},
											{
													"id": 401102,
													"name": "题库管理",
													"level": 2,
													"url": "/maintainance/training/bank/index",
													"parentId": 4011,
													"children": null
											},
											{
													"id": 401103,
													"name": "题目管理",
													"level": 2,
													"url": "/maintainance/training/question/index",
													"parentId": 4011,
													"children": null
											},
											{
													"id": 401104,
													"name": "试卷管理",
													"level": 2,
													"url": "/maintainance/training/exam/index",
													"parentId": 4011,
													"children": null
											},
											{
													"id": 401105,
													"name": "我的考试",
													"level": 2,
													"url": "/maintainance/training/exam/my",
													"parentId": 4011,
													"children": null
											},
									]
							},
					]
			)
      setTimeout(() => {
        this.loading = false;
      }, 100);
    },
  },
};
</script>

<style scoped lang="less">
.logo {
  .flex-row-center-center();
  padding: 0 10px;
  width: 100%;
  text-align: center;
  height: 60px;
  background: @base-color;
  color: @base-font-color-white;
  cursor: pointer;
  text-overflow: ellipsis;
  overflow: hidden;
  white-space: nowrap;
}

.collapse {
  position: absolute;
  right: -40px;
  top: 22px;
  cursor: pointer;
  color: @base-font-color;
}

.menus {
  flex: 1;
  overflow: hidden;
  overflow-y: auto;
  min-width: @menus-base-width;
  background: @layout-background-color;

  .el-sub-menu__title span {
    box-sizing: border-box;
    padding-right: 15px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
}
</style>
