<template>
	<div class="wrap">
		<div class="cus-header">
			<el-form :model="searchForm" label-width="80px">
				<!-- 基础查询条件 -->
				<div class="form-item-grid" :class="[isExpanded ? 'is-expanded' : '']">
          <el-form-item label="省份">
						<el-select v-model="searchForm.provinceId" placeholder="选择省份" clearable style="width: 100%;">
							<el-option v-for="(item, index) in provinceOptions" :value="item.id" :label="item.name"
											:key="index" />
						</el-select>
					</el-form-item>

					<el-form-item label="年度">
						<el-date-picker
              v-model="searchForm.year"
              type="year"
              placeholder="选择年度"
              clearable
							value-format="YYYY"
            />
					</el-form-item>	

					<!-- 查询按钮组 -->
					<div class="search-buttons">
						<el-button type="default" @click="onReset">重置</el-button>
						<el-button type="primary" @click="queryList">查询</el-button>
          <!-- <el-link type="primary" @click="toggleExpand" :underline="false" style="margin-left: 10px;">
            {{ isExpanded ? '收起' : '展开' }}
            <el-icon>
              <arrow-up v-if="isExpanded" />
              <arrow-down v-else />
            </el-icon>
          </el-link> -->

					</div>
				</div>
			</el-form>
		</div>
		<div class="cus-main" ref="mainRef">
			<div class="cus-list" v-loading="loading" ref="cusListRef">
				<div style="text-align: right;">
					<el-button type="primary" plain >上传电费单价</el-button>
					<el-button type="primary" plain >下载模板</el-button>
				</div>
				<el-table :data="tableData">
          <el-table-column
            prop="provinceName"
            label="省份"
            fixed="left"
            min-width="150"
          />
          <el-table-column
            v-for="col in monthColumns"
            :key="col.key"
            :prop="col.key"
            :label="col.title"
            :width="col.width"
          >
            <template #default="{ row }">
              <div v-if="isEditing(row, col)" class="edit-cell">
                <el-input v-model="editingValue" size="small" @keyup.enter="handleSave(row, col)" style="width: 80px;" />
                <div class="edit-cell-actions">
                  <el-button type="primary" link size="small" @click="handleSave(row, col)" :loading="saveLoading">保存</el-button>
                  <el-button type="default" link size="small" @click="handleCancelEdit">取消</el-button>
                </div>
              </div>
              <div v-else @click="handleCellClick(row, col)" class="view-cell">
                {{ row[col.key] }}
              </div>
            </template>
          </el-table-column>
        </el-table>
			</div>
		</div>
	</div>
</template>

<script setup>
import { ref, reactive, onMounted, computed, nextTick } from 'vue';
import API from '@/api/maintainance';
import _API from '@/api/epc';
import { ElMessage } from 'element-plus';

const tableData = ref([])
const columns = ref([])

const isExpanded = ref(false);
const provinceOptions = ref([]);
const addressList = ref({});

const searchForm = reactive({
	provinceId: '',
	year: '',
});
const loading = ref(false)

const editingCell = ref(null); 
const editingValue = ref('');
const saveLoading = ref(false);

let tableDataIdMap = {}

const monthColumns = computed(() => {
  return columns.value.filter(c => c.key !== 'provinceName');
});

const isEditing = (row, column) => {
  return editingCell.value && editingCell.value.provinceName === row.provinceName && editingCell.value.monthKey === column.key;
};

const handleCellClick = (row, column) => {
  if (saveLoading.value) return; 
  if (editingCell.value && (editingCell.value.provinceName !== row.provinceName || editingCell.value.monthKey !== column.key)) {
     handleCancelEdit();
  }

  editingCell.value = {
    provinceName: row.provinceName,
    monthKey: column.key, 
    originalValue: row[column.key]
  };
  editingValue.value = row[column.key] === '-' ? '' : row[column.key];
};

const handleSave = async (row, column) => {
  if (editingValue.value === editingCell.value.originalValue) {
    handleCancelEdit(); 
    return;
  }
  saveLoading.value = true;
  const province = provinceOptions.value.find(p => p.name === row.provinceName);
  if (!province) {
    ElMessage.error('未找到对应的省份ID');
    saveLoading.value = false;
    return;
  }

  const params = {
		...tableDataIdMap[row.provinceName][column.key],
    price: editingValue.value,
  };

  try {
		const api = editingCell.value.originalValue ? API.updateElecPrice : API.createElecBill;
    const res = await api(params); 
    if (res.data && res.data.success) { 
      const rowIndex = tableData.value.findIndex(r => r.provinceName === row.provinceName);
      if (rowIndex !== -1) {
        tableData.value[rowIndex][column.key] = editingValue.value;
      }
      ElMessage.success('保存成功');
      handleCancelEdit(); 
    } else {
      ElMessage.error((res.data && res.data.error) || '保存失败');
    }
  } catch (error) {
    ElMessage.error('保存电费发生错误');
  } finally {
    saveLoading.value = false;
  }
};

const handleCancelEdit = () => {
  editingCell.value = null;
  editingValue.value = '';
};

const getList = async() => {
  handleCancelEdit();
  loading.value = true;
  try {
    const res = await API.getStationElecBillByYear(searchForm);
    if (res.success) {
      processElecBillData(res.result)
    } else {
      ElMessage.error(res.error || '获取列表失败');
    }
  } finally{
    loading.value = false
  }
  
}

const processElecBillData = (data) => {
  if (!data || Object.keys(data).length === 0) {
    return;
  }

  const provinces = Object.keys(data);
  const yearMonthSet = new Set();

  provinces.forEach(provinceName => {
    const provinceRecords = data[provinceName];
    if (Array.isArray(provinceRecords)) {
      provinceRecords.forEach(record => {
        if (record && record.year && record.month !== undefined) {
          yearMonthSet.add(`${record.year}${String(record.month).padStart(2, '0')}`);
        }
      });
    }
  });

  const sortedYearMonths = Array.from(yearMonthSet).sort((a, b) => {
    const yearA = parseInt(a.substring(0, 4), 10);
    const monthA = parseInt(a.substring(4, 6), 10);
    const yearB = parseInt(b.substring(0, 4), 10);
    const monthB = parseInt(b.substring(4, 6), 10);
    if (yearA !== yearB) {
      return yearA - yearB;
    }
    return monthA - monthB;
  });

  const dynamicColumns = sortedYearMonths.map(ym => ({
    key: ym,
    dataKey: ym,
    title: ym,
    width: 200,
  }));

  columns.value = [
    { key: 'provinceName', dataKey: 'provinceName', title: '省份', fixed: 'left', minWidth: 150 },
    ...dynamicColumns
  ];

	tableDataIdMap = {}

  const processedTableData = provinces.map(provinceName => {
    const row = { provinceName };
    const provinceRecords = data[provinceName] || [];
    const priceMap = new Map();
		tableDataIdMap[provinceName] = {}

    if (Array.isArray(provinceRecords)) {
      provinceRecords.forEach(record => {
        if (record && record.year && record.month !== undefined && record.price !== undefined) {
					const key = `${record.year}${String(record.month).padStart(2, '0')}`
          priceMap.set(key, record.price);
					tableDataIdMap[provinceName][key] = record
        }
      });
    }

    sortedYearMonths.forEach(ym => {
      row[ym] = priceMap.get(ym) !== undefined ? priceMap.get(ym) : '-';
    });
    return row;
  });
  tableData.value = processedTableData;
}; 

const queryList = () => {
  getList()
}

const onReset = () => {
	Object.assign(searchForm, {
		provinceId: '',
	  year: '',
	});
	queryList();
};

const toggleExpand = () => {
	isExpanded.value = !isExpanded.value;
};

const getAreaList = () => {
	_API.getRegionList().then(res => {
		if (res.success && res.result) {
			for (let i in res.result.province_list) {
				provinceOptions.value.push({ id: i, name: res.result.province_list[i] });
			}
			addressList.value = res.result;
		}
	});
};

onMounted(() => {
	getList();
  getAreaList();
});
</script>

<style lang="less" scoped>
@import '@/assets/style/_cus_header.less';
@import '@/assets/style/_cus_list.less';

.wrap {
	height: 100%;
}

.cus-header {
	margin-bottom: 0px;

	.form-item-grid {
		display: grid;
		grid-template-columns: repeat(3, 1fr);
		gap: 12px;
		width: 100%;

		/* 默认状态（收起）- 只显示前两个元素和按钮组 */
		.el-form-item:nth-child(n+3):not(:last-child) {
			display: none;
		}

		/* 展开状态 - 显示所有元素 */
		&.is-expanded {
			.el-form-item:nth-child(n+3):not(:last-child) {
				display: flex;
			}
		}
	}

	.search-buttons {
		display: flex;
		align-items: center;
		justify-content: flex-end;
		grid-column: -2 / -1;
	}

	.el-form-item {
		width: 100%;
		margin-bottom: 0px;

		.el-input,
		.el-select,
		.el-date-editor {
			width: 100%;
		}
	}
}

.cus-main {
	display: flex;
	flex-direction: column;
	flex: 1;
	overflow: hidden;

	>.el-button {
		margin-bottom: 10px;
	}

	.cus-list {
		display: flex;
		flex-direction: column;
		flex: 1;
		overflow: hidden;
		position: relative;

		// 表格自动填充剩余空间
		.cus-table {
			flex: 1;
			overflow: auto;
			height: 100%;
		}

		// 分页控件固定在底部
		.cus-pages {
			margin-top: 10px;
		}
	}
}

.edit-cell {
  display: flex;
  align-items: center;
}
.edit-cell-actions {
  margin-left: 8px;
  display: flex;
  align-items: center;
  .el-button + .el-button {
    margin-left: 6px;
  }
}
.view-cell {
  cursor: pointer;
  min-height: 24px; 
  display: flex;
  align-items: center;
}
</style>
