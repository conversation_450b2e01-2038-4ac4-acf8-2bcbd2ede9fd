<template>
  <el-dialog title="备注" v-model="dialogVisible" width="500px" :close-on-click-modal="false" @close="handleClose" append-to-body>
    <el-form ref="formRef" :model="form" :rules="rules" label-width="100px">
      <el-form-item label="补充电费(元)" prop="supplementAmount">
        <el-input-number v-model="form.supplementAmount" :precision="2" :step="1" :min="0" placeholder="请输入补充电费" style="width:100%" />
      </el-form-item>
      <el-form-item label="备注" prop="remark">
        <el-input v-model="form.remark" type="textarea" :rows="3" placeholder="请输入备注" />
      </el-form-item>
    </el-form>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="handleClose" :disabled="isSubmitting">取 消</el-button>
        <el-button type="primary" @click="submitForm" :loading="isSubmitting">确 定</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, reactive, watch } from 'vue';
import { ElMessage } from 'element-plus';
import API from '@/api/maintainance';
import _ from 'lodash';

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  data: {
    type: Object,
    default: () => ({})
  }
})

const emit = defineEmits(['update:visible', 'success']);

const dialogVisible = ref(false);
const formRef = ref(null);
const isSubmitting = ref(false);

const form = reactive({
  id: '',
  supplementAmount: null,
  remark: ''
})

const rules = reactive({
  supplementAmount: [
    { type: 'number', message: '补充电费必须为数字值', trigger: 'blur' }
  ],
  remark: [
    { max: 200, message: '备注不能超过200个字符', trigger: 'blur' }
  ]
})

watch(() => props.visible, (val) => {
  dialogVisible.value = val
  if (val && props.data.id) {
    form.id = props.data.id;
    form.supplementAmount = props.data.supplementAmount;
    form.remark = props.data.remark || '';
  }
}, { immediate: true })

const submitForm = async () => {
  if (isSubmitting.value) return;
  if (!formRef.value) return;

  const valid = await formRef.value.validate();
  if (!valid) return;

  isSubmitting.value = true;
  try {
    const formData = _.cloneDeep(form);
    // 接口 updateStationElecBill 预期接收整个对象，其中包含 id
    const res = await API.updateStationElecBill(formData);
    if (!res?.data?.success) {
      ElMessage.error(res?.data?.error || '保存失败');
      return;
    }

    ElMessage.success('保存成功');
    emit('success');
    handleClose();
  } catch (error) {
    console.error('保存备注失败:', error);
    ElMessage.error('系统异常，请稍后重试');
  } finally {
    isSubmitting.value = false;
  }
};

const handleClose = () => {
  emit('update:visible', false);
  formRef.value?.resetFields();
  form.id = '';
  form.supplementAmount = null;
  form.remark = '';
}
</script>

<style lang="less" scoped>
.dialog-footer {
  text-align: right;
}
</style>
