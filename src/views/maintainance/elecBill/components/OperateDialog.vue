<template>
  <el-drawer :title="'提报工单'" v-model="dialogVisible" size="50%" :close-on-click-modal="false" :before-close="handleClose"
    destroy-on-close direction="rtl">
    <el-form ref="formRef" :model="form" :rules="rules" label-width="100px" class="drawer-form">
      <el-form-item label="故障名称" prop="configId">
        <el-select v-model="form.configId" placeholder="请选择故障" clearable filterable style="width: 100%;">
          <el-option v-for="item in faultCategoryOptions" :key="item.value" :label="item.label" :value="item.value" />
        </el-select>
      </el-form-item>
      <el-form-item label="现场照片" prop="photos">
        <HMultiUpload v-model="form.photos" :limit="5" accept="image/*" tip="最多上传5张图片" />
      </el-form-item>
      <el-form-item label="问题描述" prop="problemDesc">
        <el-input v-model="form.problemDesc" type="textarea" :rows="3" placeholder="请输入问题描述" />
      </el-form-item>
    </el-form>
    <div class="drawer-footer">
      <el-button @click="handleClose" :disabled="isSubmitting">取 消</el-button>
      <el-button type="primary" @click="submitForm" :loading="isSubmitting">确 定</el-button>
    </div>
  </el-drawer>
</template>

<script setup>
import { ref, reactive, watch } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import API from '@/api/maintainance'
import HMultiUpload from '@/components/HMultiUpload/index.vue'

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  data: {
    type: Object,
    default: () => ({})
  },
  stationCode: {
    type: String,
    required: true
  }
})

const emit = defineEmits(['update:visible', 'success'])

const dialogVisible = ref(false)
const formRef = ref(null)
const isSubmitting = ref(false)
const faultCategoryOptions = ref([])

const form = reactive({
  id: '',
  configId: null,
  orderSource: 'ELEC_BILL',
  photos: [],
  problemDesc: '',
  stationCode: null
})

const rules = reactive({
  configId: [
    { required: true, message: '请选择故障', trigger: 'change' }
  ],
  problemDesc: [
    { max: 500, message: '问题描述不能超过500个字符', trigger: 'blur' }
  ],
  photos: [
    { required: true, message: '请上传图片', trigger: 'change' }
  ]
})

const fetchFaultCategories = async () => {
  try {
    const res = await API.getWorkOrderConfigList({ orderType: 'report' })
    if (res.success && res.result) {
      faultCategoryOptions.value = res.result.map(item => ({
        value: item.id,
        label: item.configName
      }))
    } else {
      ElMessage.error(res.error || '获取故障列表失败')
      faultCategoryOptions.value = []
    }
  } catch (error) {
    console.error('获取故障列表失败:', error)
    ElMessage.error('获取故障列表接口调用失败')
    faultCategoryOptions.value = []
  }
}

watch(() => props.visible, (val) => {
  dialogVisible.value = val
  if (val) {
    fetchFaultCategories()
  }
}, { immediate: true })

const submitForm = async () => {
  if (isSubmitting.value) return
  if (!formRef.value) return
  try {
    await formRef.value.validate()
    await ElMessageBox.confirm('您确定要提交吗？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    })
    isSubmitting.value = true
    const photosString = Array.isArray(form.photos) ? form.photos.join(',') : form.photos
    const params = { ...form, photos: photosString, stationCode: props.stationCode }
    delete params.id
    const res = await API.submithWorkOrder(params)
    if (res.data.success) {
      ElMessage.success('提报成功')
      emit('success')
      handleClose()
    } else {
      ElMessage.error(res.data.error || '提报失败')
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('表单验证失败或提交失败:', error)
    }
  } finally {
    isSubmitting.value = false
  }
}

const resetForm = () => {
  Object.assign(form, {
    id: '',
    configId: null,
    orderSource: 'ELEC_BILL',
    photos: [],
    problemDesc: ''
  })
  if (formRef.value) {
    formRef.value.clearValidate()
  }
}

const handleClose = () => {
  emit('update:visible', false)
  resetForm()
}
</script>

<style lang="less" scoped>
.drawer-form {
  height: calc(100% - 70px);
  overflow-y: auto;
}

.drawer-footer {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  padding: 16px 20px;
  background-color: #fff;
  border-top: 1px solid #e4e7ed;
  text-align: right;
  box-sizing: border-box;

  .el-button {
    margin-left: 8px;
  }
}
</style>