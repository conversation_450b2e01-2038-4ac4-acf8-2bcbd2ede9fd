<template>
	<div class="wrap">
		<div class="cus-header">
			<el-form :model="searchForm" label-width="80px">
				<!-- 基础查询条件 -->
				<div class="form-item-grid" :class="[isExpanded ? 'is-expanded' : '']">
					<el-form-item label="报表">
						<el-select v-model="searchForm.reportConfigId" style="width: 100%;" @change="handleReportConfigChange">
							<el-option v-for="item in reportConfigOptions" :key="item.value" :label="item.label"
								:value="item.value" />
						</el-select>
					</el-form-item>

					<el-form-item v-if="hasConditionList.includes('subCenterName')" label="分中心">
            <el-select v-model="searchForm.subCenterCode" placeholder="选择分中心" clearable style="width: 100%;">
              <el-option v-for="item in subCenterOptions" :key="item.value" :label="item.label" :value="item.value" />
            </el-select>
          </el-form-item>

					<!-- 展开的查询条件 -->
          <el-form-item label="运维商" v-if="hasConditionList.includes('opName')">
            <el-input v-model="searchForm.opName" placeholder="输入运维商" clearable />
          </el-form-item>
          <el-form-item label="资方所属" v-if="hasConditionList.includes('specialFlag')">
            <el-select v-model="searchForm.specialFlag" placeholder="选择资方所属" clearable style="width: 100%;">
              <el-option v-for="item in capitalBelongOptions" :key="item.value" :label="item.label"
                :value="item.value" />
            </el-select>
          </el-form-item>

					<!-- 查询按钮组 -->
					<div class="search-buttons">
						<el-button type="default" @click="onReset">重置</el-button>
						<el-button type="primary" @click="queryList">查询</el-button>
						<el-link v-show="hasConditionList.length > 0" type="primary" @click="toggleExpand" :underline="false" style="margin-left: 10px;">
							{{ isExpanded ? '收起' : '展开' }}
							<el-icon>
								<arrow-up v-if="isExpanded" />
								<arrow-down v-else />
							</el-icon>
						</el-link>
					</div>
				</div>
			</el-form>
		</div>
		<div class="cus-main" ref="mainRef">
			<div class="cus-list" v-loading="loading" ref="cusListRef">
				<div style="text-align: right;">
					<el-button type="primary" plain>导出</el-button>
				</div>
				<el-table :data="listArr" class="cus-table">
					<el-table-column fixed align="center" type="index" label="序号" width="60" />
					<el-table-column
            v-for="col in columns"
            :key="col.key"
            :prop="col.key"
            :label="col.title"
            :minWidth="col.minWidth"
          >
            <template #default="{ row }">
              <div class="view-cell">
                {{ row[col.key] }}
              </div>
            </template>
          </el-table-column>
				</el-table>
				<el-pagination class="cus-pages" v-if="total" background layout="sizes, prev, pager, next, ->, total"
					:page-sizes="[10, 20, 30]" :page-size="pagination.pageSize" :current-page="pagination.pageNum" :total="total"
					@size-change="changeSize" @current-change="changeCurrent" />
			</div>
		</div>
	</div>
</template>

<script setup>
import { ref, reactive, onMounted, onActivated } from 'vue';
import { useTablePagination } from '@/composables/useTablePagination';
import API from '@/api/maintainance';
import { ArrowDown, ArrowUp } from '@element-plus/icons-vue';
import _D from '@/edata/_osp_data';

const isExpanded = ref(false);
const searchForm = reactive({
  reportConfigId: '',
	specialFlag: null,
	subCenterCode: null
});
const reportConfigOptions = ref([]);
const columns = ref([])
const subCenterOptions = _D.subCenterList;
const capitalBelongOptions = _D.property;
const conditionList = ["subCenterName", "specialFlag", "opName"]
const hasConditionList = ref([])

const {
	loading,
	listArr,
	total,
	pagination,
	getList,
	queryList,
	changeSize,
	changeCurrent
} = useTablePagination(
	API.getReportCenterConfigData,
	() => ({ ...searchForm }),
	{ manual: true }
);

const onReset = () => {
	Object.assign(searchForm, {
    opName: '',
    specialFlag: null,
		subCenterCode: null
	});
	queryList();
};

const toggleExpand = () => {
	isExpanded.value = !isExpanded.value;
};

const fetchReportConfigList = async () => {
  const res = await API.getReportCenterConfigList();
  if(res.success === true) {
    reportConfigOptions.value = res.result.map(item => {
      return {
        label: item.reportName,
        value: item.id
      }
    })
  }
}

const fetchReportConfigDetail = async () => {
	if(!searchForm.reportConfigId) {
		return
	}
  API.getReportConfig({ id: searchForm.reportConfigId }).then(res => {
    columns.value = res.result?.fields.map(item => {
      return {
        key: item.fieldCode,
        title: item.fieldName,
        minWidth: Math.max(120, item.fieldName.length * 18 + 30)
      }
    }) || []
		hasConditionList.value = conditionList?.filter(item => columns.value.findIndex(ele => ele.key === item) !== -1) || []
  })
}

const handleReportConfigChange = async (val) => {
  searchForm.reportConfigId = val;
	hasConditionList.value = [];
	Object.assign(searchForm, {
    opName: '',
    specialFlag: null,
		subCenterCode: null
	});
  fetchReportConfigDetail();
  getList();
}

onMounted(async() => {
  loading.value = true;
  await fetchReportConfigList();
  searchForm.reportConfigId = reportConfigOptions.value[0].value;
  fetchReportConfigDetail();
	getList();
});

onActivated(() => {
	if(searchForm.reportConfigId) {
		fetchReportConfigDetail();
		getList();
	}
})
</script>

<style lang="less" scoped>
@import '@/assets/style/_cus_header.less';
@import '@/assets/style/_cus_list.less';

.wrap {
	height: 100%;
}

.cus-header {
	margin-bottom: 0px;

	.form-item-grid {
		display: grid;
		grid-template-columns: repeat(3, 1fr);
		gap: 12px;
		width: 100%;

		/* 默认状态（收起）- 只显示前两个元素和按钮组 */
		.el-form-item:nth-child(n+3):not(:last-child) {
			display: none;
		}

		/* 展开状态 - 显示所有元素 */
		&.is-expanded {
			.el-form-item:nth-child(n+3):not(:last-child) {
				display: flex;
			}
		}
	}

	.search-buttons {
		display: flex;
		align-items: center;
		justify-content: flex-end;
		grid-column: -2 / -1;
	}

	.el-form-item {
		width: 100%;
		margin-bottom: 0px;

		.el-input,
		.el-select,
		.el-date-editor {
			width: 100%;
		}
	}
}

.cus-main {
	display: flex;
	flex-direction: column;
	flex: 1;
	overflow: hidden;

	>.el-button {
		margin-bottom: 10px;
	}

	.cus-list {
		display: flex;
		flex-direction: column;
		flex: 1;
		overflow: hidden;
		position: relative;

		// 表格自动填充剩余空间
		.cus-table {
			flex: 1;
			overflow: auto;
			height: 100%;
		}

		// 分页控件固定在底部
		.cus-pages {
			margin-top: 10px;
		}
	}
}
</style>
