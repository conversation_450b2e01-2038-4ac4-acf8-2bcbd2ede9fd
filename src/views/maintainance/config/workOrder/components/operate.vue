<template>
  <el-drawer :title="form.id ? '编辑工单配置' : '新增工单配置'" v-model="dialogVisible" size="650px" :close-on-click-modal="false"
    :before-close="handleClose" direction="rtl">
    <el-form ref="formRef" :model="form" :rules="rules" label-width="140px" class="drawer-form">
      <el-form-item label="工单配置名称" prop="configName">
        <el-input v-model="form.configName" placeholder="请输入工单配置名称" />
      </el-form-item>

      <el-form-item label="工单类型" prop="orderType">
        <el-select style="width: 100%" v-model="form.orderType" placeholder="请选择工单类型">
          <el-option v-for="item in workOrderTypeOptions" :key="item.value" :label="item.label" :value="item.value" />
        </el-select>
      </el-form-item>

      <el-form-item label="关联故障" prop="faultId">
        <el-select style="width: 100%" v-model="form.faultId" placeholder="请选择关联故障" filterable>
          <el-option v-for="item in faultOptions" :key="item.value" :label="item.label" :value="item.value" />
        </el-select>
      </el-form-item>

      <el-form-item label="关联方案" prop="solutionId">
        <el-select v-model="form.solutionId" placeholder="请选择关联方案" style="width: 100%" clearable filterable>
          <el-option v-for="item in solutionOptions" :key="item.value" :label="item.label" :value="item.value" />
        </el-select>
      </el-form-item>

      <el-form-item label="下发方式" prop="dispatchMode">
        <el-select v-model="form.dispatchMode" placeholder="请选择下发方式" style="width: 100%"
          @change="handleDispatchModeChange">
          <el-option v-for="item in dispatchModeOptions" :key="item.value" :label="item.label" :value="item.value" />
        </el-select>
      </el-form-item>

      <el-form-item label="下发审核权限" prop="dispatchReviewPermission" v-if="form.dispatchMode === 'REVIEW'">
        <el-select v-model="form.dispatchReviewPermission" placeholder="请选择下发审核权限" style="width: 100%">
          <el-option v-for="item in dispatchReviewPermissionOptions" :key="item.value" :label="item.label"
            :value="item.value" />
        </el-select>
      </el-form-item>

      <el-form-item label="审核方式" prop="reviewMode">
        <el-select v-model="form.reviewMode" placeholder="请选择审核方式" style="width: 100%">
          <el-option v-for="item in reviewModeOptions" :key="item.value" :label="item.label" :value="item.value" />
        </el-select>
      </el-form-item>

      <el-form-item label="超时时间" prop="timeoutDays">
        <el-radio-group v-model="form.timeoutDays">
          <el-radio :label="1">1天</el-radio>
          <el-radio :label="2">2天</el-radio>
          <el-radio :label="3">3天</el-radio>
          <el-radio :label="5">5天</el-radio>
          <el-radio :label="10">10天</el-radio>
          <el-radio :label="15">15天</el-radio>
        </el-radio-group>
      </el-form-item>

      <el-form-item label="状态" prop="status">
        <el-switch v-model="form.status" :active-value="1" :inactive-value="0" />
      </el-form-item>

      <el-form-item label="备注" prop="remark">
        <el-input v-model="form.remark" type="textarea" :rows="3" placeholder="请输入备注" />
      </el-form-item>

      <!-- 检查项列表 -->
      <el-form-item label="检查项列表">
        <div class="check-items-container">
          <div v-for="(item, index) in form.checkItems" :key="index" class="check-item">
            <div class="check-item-header">
              <span class="check-item-title">检查项 {{ index + 1 }}</span>
              <el-button type="danger" size="small" @click="removeCheckItem(index)" plain>删除</el-button>
            </div>

            <el-form-item label="检查项" :prop="`checkItems.${index}.checkItem`"
              :rules="[{ required: true, message: '请输入检查项', trigger: 'blur' }, { max: 20, message: '检查项不能超过20个字符', trigger: 'blur' }, { validator: validateNoWhitespace, trigger: 'blur' }]">
              <el-input v-model="item.checkItem" placeholder="请输入检查项" />
            </el-form-item>

            <el-form-item label="回填类型" :prop="`checkItems.${index}.resultType`"
              :rules="{ required: true, message: '请选择回填类型', trigger: 'change' }">
              <el-select v-model="item.resultType" placeholder="请选择回填类型" style="width: 100%">
                <el-option v-for="dictItem in inputTypeOptions" :key="dictItem.value" :label="dictItem.label"
                  :value="dictItem.value" />
              </el-select>
            </el-form-item>

            <el-form-item v-if="item.resultType === 'image'" label="示例图片" :prop="`checkItems.${index}.exampleImage`"
              :rules="{ required: true, message: '请上传示例图片', trigger: 'change' }">
              <h-upload class="avatar-uploader" :modelValue="item.exampleImage"
                @update:modelValue="(resUrl) => item.exampleImage = resUrl" comTypes="image" imgNames="示例图片"
                uploadTip="仅支持jpg、jpeg、png格式的图片">
              </h-upload>
            </el-form-item>
            <el-form-item label="排序" :prop="`checkItems.${index}.sort`">
              <el-input-number v-model="item.sort" :min="1" :max="999" />
            </el-form-item>
          </div>

          <div class="add-check-item">
            <el-button type="primary" @click="addCheckItem" plain>添加检查项</el-button>
          </div>
        </div>
      </el-form-item>
    </el-form>

    <div class="drawer-footer">
      <el-button @click="handleClose" :disabled="isSubmitting">取 消</el-button>
      <el-button type="primary" @click="submitForm" :loading="isSubmitting">确 定</el-button>
    </div>
  </el-drawer>
</template>

<script setup>
import { ref, reactive, watch, onMounted, computed } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import API from '@/api/maintainance'
import _ from 'lodash'
import HUpload from '@/components/HUpload/index.vue'
import { validateNoWhitespace } from '@/utils/validateRule'
import { useDictStore } from '@/stores/modules/dict'

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  data: {
    type: Object,
    default: () => ({})
  }
})

const emit = defineEmits(['update:visible', 'success'])

const dialogVisible = ref(false)
const formRef = ref(null)
const isSubmitting = ref(false)
const solutionOptions = ref([])
const faultOptions = ref([])

const dictStore = useDictStore()

const workOrderTypeOptions = computed(() => dictStore.getDictByType('work_order_type'))
const inputTypeOptions = computed(() => dictStore.getDictByType('input_type'))
const dispatchModeOptions = computed(() => dictStore.getDictByType('dispatch_mode'))
const dispatchReviewPermissionOptions = computed(() => dictStore.getDictByType('dispatch_review_permission'))
const reviewModeOptions = computed(() => dictStore.getDictByType('review_mode'))

const validateSolution = (rule, value, callback) => {
  console.log(form)
  if (form.orderType && form.orderType !== 'report' && !form.solutionId) {
    callback(new Error('请选择关联方案'))
  } else {
    callback()
  }
}

// 获取方案列表
const getSolutionOptions = async (solutionType) => {
  try {
    const res = await API.getSolutionList({ solutionType })
    if (res?.success && res?.result) {
      solutionOptions.value = res.result.map(item => ({
        label: item.name,
        value: item.id
      }))
    }
  } catch (error) {
    console.error('获取方案列表失败:', error)
  }
}

// 获取故障列表
const getFaultOptions = async (workOrderType) => {
  try {
    const res = await API.getFaultCategoryList({ workOrderType })
    if (res?.success && res?.result) {
      faultOptions.value = res.result.map(item => ({
        label: item.name,
        value: item.id
      }))
    }
  } catch (error) {
    console.error('获取故障列表失败:', error)
  }
}

onMounted(() => {
  if (form.orderType) {
    getFaultOptions(form.orderType)
    getSolutionOptions(form.orderType)
  }
})

let isTriggerManual = true

const form = reactive({
  id: '',
  configName: '',
  orderType: '',
  dispatchMode: 'AUTO',
  dispatchReviewPermission: null,
  reviewMode: 'NONE',
  timeoutDays: 3,
  status: 1,
  remark: '',
  faultId: null,
  solutionId: null,
  checkItems: [{
    id: null,
    configId: null,
    checkItem: '',
    resultType: 'text',
    sort: 1,
    exampleImage: ''
  }]
})

const rules = reactive({
  configName: [
    { required: true, message: '请输入工单配置名称', trigger: 'blur' },
    { validator: validateNoWhitespace, trigger: 'blur' },
    { max: 20, message: '工单配置名称不能超过20个字符', trigger: 'blur' }
  ],
  orderType: [
    { required: true, message: '请选择工单类型', trigger: 'change' }
  ],
  faultId: [
    { required: true, message: '请选择关联故障', trigger: 'change' }
  ],
  solutionId: [
    { validator: validateSolution, trigger: 'change' },
    // { required: true, message: '请选择关联方案', trigger: 'change' }
  ],
  dispatchMode: [
    { required: true, message: '请选择下发方式', trigger: 'change' }
  ],
  dispatchReviewPermission: [
    { required: true, message: '请选择下发审核权限', trigger: 'change' }
  ],
  reviewMode: [
    { required: true, message: '请选择审核方式', trigger: 'change' }
  ],
  timeoutDays: [
    { required: true, message: '请选择超时时间', trigger: 'blur' }
  ],
  remark: [
    { max: 500, message: '描述不能超过500个字符', trigger: 'blur' }
  ]
})

// 下发方式变化处理函数
const handleDispatchModeChange = (newVal) => {
  // 使用字典值进行判断
  // const reviewValue = dispatchModeOptions.value.find(item => item.value === 'REVIEW')?.value;
  // if (newVal !== reviewValue) {
  //   // 如果不是审核，则重置下发审核权限
  //   const subCenterValue = dispatchReviewPermissionOptions.value.find(item => item.value === 'SUB_CENTER')?.value;
  //   form.dispatchReviewPermission = subCenterValue;
  // } else {
  // }
  form.dispatchReviewPermission = null
  console.log(form.dispatchReviewPermission)
};

// 监听工单类型变化
watch(() => form.orderType, (newVal) => {
  if (newVal) {
    getFaultOptions(newVal)
    getSolutionOptions(newVal)
    // 只有在非手动触发时才清空已选择的故障和方案
    if (isTriggerManual) {
      form.faultId = null
      form.solutionId = null
    }
  } else {
    faultOptions.value = []
    solutionOptions.value = []
  }
  isTriggerManual = true
})

watch(() => props.visible, async (val) => {
  dialogVisible.value = val;
  if (val && props.data.id) {
    isTriggerManual = false;

    try {
      const res = await API.getWorkOrderConfig({ id: props.data.id });

      if (res.success && res.result) {
        const detailData = res.result;
        // 填充表单数据
        form.id = detailData.id || '';
        form.configName = detailData.configName || ''; // 接口返回的是 name
        form.orderType = detailData.orderType || '';
        form.dispatchMode = detailData.dispatchMode || 'AUTO';
        form.dispatchReviewPermission = detailData.dispatchReviewPermission || null;
        form.reviewMode = detailData.reviewMode || 'NONE';
        form.timeoutDays = detailData.timeoutDays || 3;
        form.status = detailData.status;
        form.remark = detailData.remark || '';
        form.faultId = detailData.faultId || null;
        form.solutionId = detailData.solutionId || null;
        if(form.orderType === 'report') {
          form.solutionId = null
        }

        // 填充检查项列表
        if (detailData.checkItems && detailData.checkItems.length > 0) {
          form.checkItems = detailData.checkItems.map(item => ({
            id: item.id || null,
            configId: item.configId || null, // 确保接口返回 configId
            checkItem: item.checkItem || '',
            resultType: item.resultType || 'text',
            sort: item.sort || 1,
            exampleImage: item.exampleImage || ''
          }));
        } else {
          // 如果接口没返回检查项，确保至少有一项空的
          form.checkItems = [{ id: null, configId: form.id, checkItem: '', resultType: 'text', sort: 1, exampleImage: '' }];
        }

        // 获取关联故障列表（需要在 orderType 填充之后）
        if (form.orderType) {
          await getFaultOptions(form.orderType);
          // 重新设置 faultId，因为 getFaultOptions 可能会异步更新列表
          form.faultId = detailData.faultId || null;
        }

      } else {
        ElMessage.error(res.message || '获取工单配置详情失败');
        handleClose(); // 获取失败则关闭抽屉
      }
    } catch (error) {
      console.error('获取工单配置详情失败:', error);
      ElMessage.error('获取工单配置详情失败');
    }
  } else if (val) {
    resetForm();
  }
}, { immediate: true })

// 重置表单
const resetForm = () => {
  Object.assign(form, {
    id: '',
    configName: '',
    orderType: '',
    dispatchMode: 'AUTO',
    dispatchReviewPermission: null,
    reviewMode: 'NONE',
    timeoutDays: 3,
    status: 1,
    remark: '',
    faultId: null,
    solutionId: null,
    checkItems: [
      {
        id: null,
        configId: null,
        checkItem: '',
        resultType: 'text',
        sort: 1,
        exampleImage: ''
      },
    ]
  })
}

// 关闭抽屉
const handleClose = () => {
  emit('update:visible', false)
  formRef.value?.resetFields()
  resetForm()
}

// 添加检查项
const addCheckItem = () => {
  form.checkItems.push({
    id: null,
    configId: form.id,
    checkItem: '',
    resultType: 'text',
    sort: form.checkItems.length + 1,
    exampleImage: ''
  });
};

// 删除检查项
const removeCheckItem = (index) => {
  // 确保至少保留一项
  if (form.checkItems.length <= 1) {
    ElMessage.warning('检查项列表至少需要保留一项');
    return;
  }

  // 显示确认对话框
  ElMessageBox.confirm('确定要删除该检查项吗？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    form.checkItems.splice(index, 1);

    ElMessage.success('删除成功');
  }).catch(() => {
    // 用户取消删除，不做任何操作
  });
};


// 提交表单
const submitForm = async () => {
  if (isSubmitting.value) return;
  if (!formRef.value) return;

  try {
    await formRef.value.validate();
    isSubmitting.value = true;
    const submitData = { ...form };

    const api = form.id ? API.editWorkOrderConfig : API.addWorkOrderConfig

    const res = await api(submitData)
    if (res.data.success) {
      ElMessage.success('保存成功');
      emit('success')
      handleClose()
    } else {
      ElMessage.error(res.data.error || '操作失败')
    }
  } catch (error) {
    console.error('表单验证失败:', error)
    ElMessage.warning('请完善表单信息');
  } finally {
    isSubmitting.value = false;
  }
};
</script>

<style lang="less" scoped>
.drawer-form {
  height: calc(100% - 80px);
  overflow-y: auto;
  padding-right: 12px;
}

.drawer-footer {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  padding: 16px 20px;
  background-color: #fff;
  border-top: 1px solid #e4e7ed;
  text-align: right;

  .el-button {
    margin-left: 8px;
  }
}

.check-items-container {
  border: 1px solid #ebeef5;
  border-radius: 4px;
  padding: 10px;
  margin-bottom: 20px;
  width: 100%;

  .check-item {
    border: 1px dashed #e0e0e0;
    border-radius: 4px;
    padding: 15px;
    margin-bottom: 15px;

    &:last-child {
      margin-bottom: 0;
    }

    .check-item-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 15px;

      .check-item-title {
        font-weight: bold;
        font-size: 14px;
      }
    }

    :deep(.el-form-item) {
      margin-bottom: 15px;

      &:last-child {
        margin-bottom: 0;
      }
    }
  }

  .add-check-item {
    text-align: center;
    margin-top: 15px;
  }
}

.avatar-uploader {
  :deep(.el-upload) {
    border: 1px dashed #d9d9d9;
    border-radius: 6px;
    cursor: pointer;
    position: relative;
    overflow: hidden;
    transition: var(--el-transition-duration-fast);

    &:hover {
      border-color: var(--el-color-primary);
    }
  }

  .avatar-uploader-icon {
    font-size: 28px;
    color: #8c939d;
    width: 100px;
    height: 100px;
    text-align: center;
    line-height: 100px;
  }

  .avatar {
    width: 100px;
    height: 100px;
    display: block;
  }
}
</style>
