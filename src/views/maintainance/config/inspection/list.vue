<template>
  <div class="inspection-container">
    <el-tabs v-model="activeTabName" class="inspection-tabs" @tab-change="handleTabChange">
      <el-tab-pane
        v-for="(tab, index) in tabs"
        :key="index"
        :label="tab.label"
        :name="tab.name"
      ></el-tab-pane>
    </el-tabs>

    <div class="inspection-cards" v-loading="loading" element-loading-background="rgba(255, 255, 255, 0.4)">
      <div
        v-for="(item, index) in inspectionList"
        :key="index"
        class="inspection-card"
      >
        <div class="card-title">{{ item.configName }}</div>
        <div class="card-content">
          <div class="placeholder-image">
            <img v-if="item.configType === 'TEMPORARY'" src="@/assets/images/inspect/temporary.png" />
            <img v-else-if="item.configType === 'ANNUAL'" src="@/assets/images/inspect/annual.png" />
          </div>
          <div class="card-actions">
            <el-button type="danger"  @click="handleDelete(item)">删除</el-button>
            <el-button type="primary" @click="handleEdit(item)">编辑</el-button>
          </div>
        </div>
      </div>

      <div class="inspection-card add-card" @click="handleAdd">
        <div class="add-icon">
          <el-icon><Plus /></el-icon>
        </div>
      </div>
    </div>

    <Operate
      v-model:visible="operateVisible"
      :data="operateData"
      :station-type="activeTabName"
      @success="handleOperateSuccess"
    />


  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'; 
import { ElMessage, ElMessageBox } from 'element-plus'; 
import { Plus } from '@element-plus/icons-vue';
import maintainanceAPI from '@/api/maintainance';
import Operate from './components/operate.vue';
import { useDictStore } from '@/stores/modules/dict';

const tabs = [
  { name: 'COMMON', label: '户用' },
  { name: 'CM', label: '工商业' },
  { name: 'PUB_BUILD', label: '公共租赁' }
];
const activeTabName = ref('COMMON');

const inspectionList = ref([]);
const loading = ref(false);
const dictStore = useDictStore();

const currentItemId = ref('');

const operateVisible = ref(false);
const operateData = ref({});

const handleAdd = () => {
  operateData.value = {};
  operateVisible.value = true;
};

const handleEdit = (item) => {
  operateData.value = { ...item };
  operateVisible.value = true;
};

const handleDelete = (item) => {
  if (item && item.id) {
    ElMessageBox.confirm('确定要删除这个巡检配置吗？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }).then(() => {
      const params = {
        id: item.id
      };
      loading.value = true;
      maintainanceAPI.deleteInspectionConfig(params).then(res => {
        loading.value = false;
        if (res.data.success) {
          ElMessage.success('删除成功');
          getInspectionConfigList();
        } else {
          ElMessage.error(res.data.error || '删除失败');
        }
      }).catch(error => {
        loading.value = false;
        console.error('删除巡检失败:', error);
        ElMessage.error('删除失败');
      });
    }).catch(() => {});
  } else {
    ElMessage.warning('无效的巡检项目');
  }
};

const handleTabChange = (tabName) => {
  activeTabName.value = tabName;
  getInspectionConfigList();
};

const getInspectionConfigList = () => {
  loading.value = true;
  const params = {
    stationType: activeTabName.value
  };

  maintainanceAPI.getInspectionConfigList(params).then(res => {
    loading.value = false;
    if (res.success && res.result) {
      inspectionList.value = res.result;
    } else {
      inspectionList.value = [];
      ElMessage.error(res.error || '获取巡检列表失败');
    }
  }).catch(error => {
    loading.value = false;
    console.error('获取巡检列表失败:', error);
    ElMessage.error('获取巡检列表失败');
  });
};

onMounted(() => {
  getInspectionConfigList();
  dictStore.fetchDict([
    'input_type',
  ]);
});

const handleOperateSuccess = () => {
  getInspectionConfigList();
};
</script>

<style lang="less" scoped>
.inspection-container {
  padding: 20px;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.inspection-tabs {

  :deep(.el-tabs__nav-scroll) {
    background-color: white;
    padding-left: 12px;
  }

  :deep(.el-tabs__header) {
    margin-bottom: 0px;
  }
}

.inspection-cards {
  display: flex;
  flex-wrap: wrap;
  gap: 20px;
  margin-top: 20px;
  height: 100%;

  .inspection-card {
    width: 280px;
    height: 230px;
    border: 1px solid #e4e7ed;
    border-radius: 4px;
    overflow: hidden;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);

    .card-title {
      height: 40px;
      line-height: 40px;
      text-align: center;
      font-size: 16px;
      font-weight: 500;
      background-color: #f5f7fa;
      border-bottom: 1px solid #e4e7ed;
    }

    .card-content {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: space-between;
      height: calc(100% - 40px);
      position: relative;

      .placeholder-image {
        display: flex;
        align-items: center;
        justify-content: center;

        img {
          height: 188px;
          width: 100%;
        }
      }

      .card-actions {
        width: 100%;
        display: flex;
        justify-content: center;
        gap: 10px;
        margin-top: 10px;
        position: absolute;
        bottom: 12px;
      }
    }

    &.add-card {
      display: flex;
      align-items: center;
      justify-content: center;
      cursor: pointer;
      background-color: #f5f7fa;
      border: 1px dashed #c0c4cc;

      &:hover {
        border-color: #409EFF;
        color: #409EFF;
      }

      .add-icon {
        font-size: 40px;
        color: #909399;
      }

      &:hover .add-icon {
        color: #409EFF;
      }
    }
  }
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}

.no-data {
  width: 100%;
  padding: 40px 0;
  text-align: center;
  color: #909399;
  font-size: 14px;
}
</style>
