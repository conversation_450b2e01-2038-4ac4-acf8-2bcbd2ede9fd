<template>
  <div class="wrap">
    <div class="cus-header">
      <el-form :model="formSearch" label-width="80px">
        <!-- 基础查询条件 -->
        <div class="form-item-grid" :class="[isExpanded ? 'is-expanded' : '']">

          <el-form-item label="报表名称">
            <el-input v-model="formSearch.reportName" placeholder="输入报表名称" clearable />
          </el-form-item>

          <el-form-item label="报表类型">
            <el-select v-model="formSearch.reportType" placeholder="选择报表类型" clearable style="width: 100%;">
              <el-option v-for="item in reportTypeOptions" :key="item.value" :label="item.label"
                :value="item.value" />
            </el-select>
          </el-form-item>

          <!-- 展开的查询条件 -->
          <el-form-item label="状态">
            <el-select v-model="formSearch.status" placeholder="选择状态" clearable style="width: 100%;">
              <el-option label="启用" :value="1" />
              <el-option label="禁用" :value="0" />
            </el-select>
          </el-form-item>

          <!-- 查询按钮组 -->
          <div class="search-buttons">
            <el-button type="default" @click="onReset">重置</el-button>
            <el-button type="primary" @click="queryList">查询</el-button>
            <el-link type="primary" @click="toggleExpand" :underline="false" style="margin-left: 10px;">
              {{ isExpanded ? '收起' : '展开' }}
              <el-icon>
                <arrow-up v-if="isExpanded" />
                <arrow-down v-else />
              </el-icon>
            </el-link>
          </div>
        </div>
      </el-form>
    </div>
    <div class="cus-main" ref="mainRef">
      <div class="cus-list" v-loading="loading" ref="cusListRef">
        <div style="text-align: right;">
          <el-button type="success" plain @click="handleAdd">新增配置</el-button>
        </div>
        <el-table :data="listArr" class="cus-table">
          <el-table-column fixed align="center" type="index" label="序号" width="60" />
          <el-table-column fixed align="center" prop="reportName" label="报表名称" width="150">
            <template #default="scope">
              {{ scope.row.reportName || '-' }}
            </template>
          </el-table-column>
          <el-table-column align="center" prop="reportType" label="报表类型" width="100">
            <template #default="scope">
              {{ getDictLabel('report_type', scope.row.reportType) }}
            </template>
          </el-table-column>
          <el-table-column align="center" prop="description" label="说明" min-width="180" show-overflow-tooltip>
            <template #default="scope">
              {{ scope.row.description || '-' }}
            </template>
          </el-table-column>
          <el-table-column align="center" prop="status" label="状态" width="100">
            <template #default="scope">
              {{ scope.row.status ? '启用' : '禁用' }}
            </template>
          </el-table-column>
          <el-table-column align="center" prop="createdBy" label="创建人" width="120">
            <template #default="scope">
              {{ scope.row.createdBy || '-' }}
            </template>
          </el-table-column>
          <el-table-column align="center" prop="createdAt" label="创建时间" width="180">
            <template #default="scope">
              {{ scope.row.createdAt && scope.row.createdAt.replace('T', ' ') || '-' }}
            </template>
          </el-table-column>
          <el-table-column align="center" prop="updatedBy" label="修改人" width="120">
            <template #default="scope">
              {{ scope.row.updatedBy || '-' }}
            </template>
          </el-table-column>
          <el-table-column align="center" prop="updatedAt" label="修改时间" width="180">
            <template #default="scope">
              {{ scope.row.updatedAt && scope.row.updatedAt.replace('T', ' ') || '-' }}
            </template>
          </el-table-column>
          <el-table-column fixed="right" align="center" label="操作" width="160">
            <template #default="scope">
              <el-button link type="primary" @click="handleEdit(scope.row)">编辑</el-button>
              <el-button link type="primary" v-if="scope.row.status == 1"
                @click="toggleStatus(scope.row, 0)">禁用</el-button>
              <el-button link type="primary" v-else @click="toggleStatus(scope.row, 1)">启用</el-button>
              <el-button link type="danger" @click="handleDelete(scope.row)">删除</el-button>
            </template>
          </el-table-column>
        </el-table>
        <el-pagination class="cus-pages" v-if="total" background layout="sizes, prev, pager, next, ->, total"
          :page-sizes="[10, 20, 30]" :page-size="pagination.pageSize" :current-page="pagination.pageNum" :total="total"
          @size-change="changeSize" @current-change="changeCurrent" />
      </div>
    </div>
    <Operate v-model:visible="operateVisible" :data="operateData" @success="handleOperateSuccess" />
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, computed, renderList } from 'vue';
import API from '@/api/maintainance';
import _ from 'lodash';
import { ArrowDown, ArrowUp } from '@element-plus/icons-vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import Operate from './components/Operate.vue';
import { useDictStore } from '@/stores/modules/dict';
import { useTablePagination } from '@/composables/useTablePagination';

// 响应式状态
const isExpanded = ref(false);
const formSearch = reactive({
  reportName: '',
  reportType: '',
  status: ''
});

const dictStore = useDictStore();
const reportTypeOptions = computed(() => dictStore.getDictByType('report_type'));
const operateVisible = ref(false);
const operateData = ref({});

// 使用组合式函数管理表格和分页状态
const {
  loading,
  listArr,
  total,
  pagination,
  getList,
  queryList,
  changeSize,
  changeCurrent,
} = useTablePagination(
  API.getReportConfigPage,
  () => formSearch,
  { manual: true }
);

const getDictLabel = (dictType, value) => {
  const dictMap = dictStore.getDictMapByType(dictType);
  return dictMap[value] || '-';
};

const onReset = () => {
  formSearch.reportName = '';
  formSearch.reportType = '';
  formSearch.status = '';
  queryList();
};

const toggleExpand = () => {
  isExpanded.value = !isExpanded.value;
};

const handleAdd = () => {
  operateData.value = {};
  operateVisible.value = true;
};

const handleEdit = (row) => {
  operateData.value = _.cloneDeep(row);
  operateVisible.value = true;
};

const toggleStatus = (row, status) => {
  const action = status == 1 ? '启用' : '禁用';
  ElMessageBox.confirm(`确认${action}该配置吗？`, '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    const params = {
      id: row.id,
      status
    };
    API.updateReportConfigStatus(params).then(res => {
      if (res.data.success) {
        getList();
      } else {
        ElMessage.error(res.error || `${action}失败`);
      }
    });
  });
};

const handleDelete = (row) => {
  ElMessageBox.confirm('确认删除该配置吗？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    const params = {
      id: row.id
    };
    API.deleteReportConfig(params).then(res => {
      if (res.data.success) {
        getList();
      } else {
        ElMessage.error(res.data.error || '删除失败');
      }
    });
  });
};

const handleOperateSuccess = () => {
  operateVisible.value = false;
  getList();
};

onMounted(async () => {
  await dictStore.fetchDict([
    'report_type',
    'user_role'
  ]);
  getList();
});
</script>

<style lang="less" scoped>
@import '@/assets/style/_cus_header.less';
@import '@/assets/style/_cus_list.less';

.wrap {
  height: 100%;
}

.cus-header {
  margin-bottom: 0px;

  .form-item-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 12px;
    width: 100%;

    /* 默认状态（收起）- 只显示前两个元素和按钮组 */
    .el-form-item:nth-child(n+3):not(:last-child) {
      display: none;
    }

    /* 展开状态 - 显示所有元素 */
    &.is-expanded {
      .el-form-item:nth-child(n+3):not(:last-child) {
        display: flex;
      }
    }
  }

  .search-buttons {
    display: flex;
    align-items: center;
    justify-content: flex-end;
    grid-column: -2 / -1;
  }

  .el-form-item {
    width: 100%;
    margin-bottom: 0px;

    .el-input,
    .el-select,
    .el-date-editor {
      width: 100%;
    }
  }
}

.cus-main {
  display: flex;
  flex-direction: column;
  flex: 1;
  overflow: hidden;

  >.el-button {
    margin-bottom: 10px;
  }

  .cus-list {
    display: flex;
    flex-direction: column;
    flex: 1;
    overflow: hidden;
    position: relative;

    .cus-table {
      flex: 1;
      overflow: auto;
      height: 100%;
    }

    .cus-pages {
      margin-top: 10px;
    }
  }
}
</style>