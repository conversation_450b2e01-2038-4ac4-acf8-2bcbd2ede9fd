<template>
  <el-drawer :title="form.id ? '编辑报表配置' : '新增报表配置'" v-model="dialogVisible" size="812"
    :close-on-click-modal="false" :before-close="handleClose" direction="rtl">
    <el-form ref="formRef" :model="form" :rules="rules" label-width="140px" class="drawer-form">
      <el-form-item label="报表名称" prop="reportName">
        <el-input v-model="form.reportName" placeholder="请输入报表名称" />
      </el-form-item>

      <el-form-item label="报表类型" prop="reportType">
        <el-select style="width: 100%" v-model="form.reportType" placeholder="请选择报表类型" @change="handleReportTypeChange">
          <el-option v-for="item in reportTypeOptions" :key="item.value" :label="item.label" :value="item.value" />
        </el-select>
      </el-form-item>

      <el-form-item label="关联角色" prop="roleCodes">
        <el-select v-model="form.roles" multiple placeholder="请选择关联角色" style="width: 100%">
          <el-option v-for="item in roleOptions" :key="item.value" :label="item.label" :value="item.value" />
        </el-select>
      </el-form-item>

      <el-form-item label="报表字段" prop="fields">
        <el-transfer v-model="selectedFieldKeys" target-order="push" :data="sourceFields" :titles="['数据源字段', '报表显示项']"
          :button-texts="['移除', '添加']" :format="{
            noChecked: '${total}',
            hasChecked: '${checked}/${total}'
          }" @change="handleTransferChange" filterable filter-placeholder="请输入字段名称">
        </el-transfer>
      </el-form-item>

      <el-form-item label="状态" prop="status">
        <el-switch v-model="form.status" :active-value="1" :inactive-value="0" />
      </el-form-item>

      <el-form-item label="说明" prop="description">
        <el-input v-model="form.description" type="textarea" :rows="3" placeholder="请输入说明" />
      </el-form-item>
    </el-form>

    <div class="drawer-footer">
      <el-button @click="handleClose" :disabled="isSubmitting">取 消</el-button>
      <el-button type="primary" @click="submitForm" :loading="isSubmitting">确 定</el-button>
    </div>
  </el-drawer>
</template>

<script setup>
import { ref, reactive, watch, computed } from 'vue'
import { ElMessage } from 'element-plus'
import API from '@/api/maintainance'
import { validateNoWhitespace } from '@/utils/validateRule'
import { useDictStore } from '@/stores/modules/dict'

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  data: {
    type: Object,
    default: () => ({})
  }
})

const emit = defineEmits(['update:visible', 'success'])

const dialogVisible = ref(false)
const formRef = ref(null)
const isSubmitting = ref(false)

const dictStore = useDictStore()

const reportTypeOptions = computed(() => dictStore.getDictByType('report_type'))
const roleOptions = computed(() => dictStore.getDictByType('user_role'))

// 数据源字段
const sourceFields = ref([])

// 已选择的字段key
const selectedFieldKeys = ref([])

const form = reactive({
  id: '',
  reportName: '',
  reportType: '',
  status: 1,
  description: '',
  roles: [],
  fields: []
})

const rules = reactive({
  reportName: [
    { required: true, message: '请输入报表名称', trigger: 'blur' },
    { validator: validateNoWhitespace, trigger: 'blur' },
    { max: 20, message: '报表名称不能超过20个字符', trigger: 'blur' }
  ],
  reportType: [
    { required: true, message: '请选择报表类型', trigger: 'change' }
  ],
  description: [
    { max: 500, message: '说明不能超过500个字符', trigger: 'blur' }
  ],
  roles: [
    { required: true, message: '请选择关联角色', trigger: 'change' }
  ],
  fields: [
    { type: 'array', required: true, message: '请选择报表字段', trigger: 'blur' }
  ]
})

const handleReportTypeChange = (val, reset = true) => {
  API.getReportConfigDataFields({ reportType: val }).then(res => {
    sourceFields.value = res.result.map(item => ({
      key: item.fieldCode,
      label: item.fieldName,
    }));
    if (reset === true) {
      form.fields = [];
      selectedFieldKeys.value = [];
    }
  })
}

// 处理Transfer组件的变化
const handleTransferChange = (_, direction, movedKeys) => {
  if (direction === 'right') {
    // 添加字段
    const fieldsToAdd = movedKeys.map(key => {
      const sourceField = sourceFields.value.find(item => item.key === key);
      return {
        fieldCode: sourceField.key,
        fieldName: sourceField.label,
      };
    });
    form.fields.push(...fieldsToAdd);
  } else {
    // 移除字段
    movedKeys.forEach(key => {
      const sourceField = sourceFields.value.find(item => item.key === key);
      const index = form.fields.findIndex(item => item.fieldName === sourceField.label);
      if (index !== -1) {
        form.fields.splice(index, 1);
      }
    });
  }
}

watch(() => props.visible, async (val) => {
  dialogVisible.value = val;
  if (val && props.data.id) {
    try {
      const res = await API.getReportConfig({ id: props.data.id });
      if (res.success && res.result) {
        const detailData = res.result;
        form.id = detailData.id || '';
        form.reportName = detailData.reportName || '';
        form.reportType = detailData.reportType || '';
        form.status = detailData.status;
        form.description = detailData.description || '';
        form.roles = detailData.roles?.map(item => item.roleCode) || [];
        handleReportTypeChange(form.reportType, false)
        if (detailData.fields && detailData.fields.length > 0) {
          form.fields = detailData.fields;

          // 设置已选择的字段
          console.log(form.fields);
          selectedFieldKeys.value = form.fields.map(item => item.fieldCode);
        } else {
          form.fields = [];
          selectedFieldKeys.value = [];
        }
      } else {
        ElMessage.error(res.message || '获取报表配置详情失败');
        handleClose();
      }
    } catch (error) {
      console.error('获取报表配置详情失败:', error);
      ElMessage.error('获取报表配置详情失败');
    }
  } else if (val) {
    resetForm();
  }
}, { immediate: true })

const resetForm = () => {
  Object.assign(form, {
    id: '',
    reportName: '',
    reportType: '',
    status: 1,
    description: '',
    roles: [],
    fields: []
  });
  sourceFields.value = [];
  selectedFieldKeys.value = [];
}

const handleClose = () => {
  emit('update:visible', false);
  formRef.value?.resetFields();
  resetForm();
}

const submitForm = async () => {
  if (isSubmitting.value) return;
  if (!formRef.value) return;

  try {
    await formRef.value.validate();
    isSubmitting.value = true;
    console.log(form.fields)
    const submitData = { ...form, roles: form.roles.map(item => ({ roleCode: item })), fields: form.fields.map((item,index) => ({ ...item, sortNo: index + 1 })) };

    const api = form.id ? API.updateReportConfig : API.createReportConfig;

    const res = await api(submitData);
    if (res.data.success) {
      ElMessage.success('保存成功');
      emit('success');
      handleClose();
    } else {
      ElMessage.error(res.data.error || '操作失败');
    }
  } catch (error) {
    console.error('表单验证失败:', error);
    ElMessage.warning('请完善表单信息');
  } finally {
    isSubmitting.value = false;
  }
};
</script>

<style lang="less" scoped>
.drawer-form {
  height: calc(100% - 80px);
  overflow-y: auto;
  padding-right: 12px;

  :deep(.el-transfer) {
    display: flex;
    align-items: center;
  }
}

.drawer-footer {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  padding: 16px 20px;
  background-color: #fff;
  border-top: 1px solid #e4e7ed;
  text-align: right;

  .el-button {
    margin-left: 8px;
  }
}

.fields-table {
  border: 1px solid #ebeef5;
  border-radius: 4px;
  margin-top: 20px;

  .table-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px;
    border-bottom: 1px solid #ebeef5;

    .table-title {
      font-weight: bold;
      font-size: 14px;
    }

    .count-info {
      font-size: 14px;
      color: #606266;
    }
  }
}
</style>
