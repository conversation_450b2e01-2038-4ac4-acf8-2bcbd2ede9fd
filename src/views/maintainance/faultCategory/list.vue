<template>
	<div class="wrap">
		<div class="cus-header">
			<el-form :model="searchForm" label-width="80px">
				<!-- 基础查询条件 -->
				<div class="form-item-grid" :class="[isExpanded ? 'is-expanded' : '']">
					<el-form-item label="故障名称">
						<el-input v-model="searchForm.name" placeholder="输入故障名称" clearable />
					</el-form-item>

					<el-form-item label="故障等级">
						<el-select v-model="searchForm.level" placeholder="选择故障等级" clearable style="width: 100%;">
							<el-option v-for="item in faultLevelOptions" :key="item.value" :label="item.label" :value="item.value" />
						</el-select>
					</el-form-item>

					<!-- 展开的查询条件 -->
					<el-form-item label="故障码">
						<el-input v-model="searchForm.code" placeholder="输入故障码" clearable />
					</el-form-item>

					<el-form-item label="设备型号">
						<el-input v-model="searchForm.deviceModel" placeholder="输入设备型号" clearable />
					</el-form-item>

					<el-form-item label="设备厂家">
						<el-select v-model="searchForm.manufacturer" placeholder="选择设备厂家" clearable style="width: 100%;">
							<el-option v-for="item in manufacturerOptions" :key="item.value" :label="item.label"
								:value="item.value" />
						</el-select>
					</el-form-item>

					<el-form-item label="工单类型">
						<el-select v-model="searchForm.workOrderType" placeholder="选择工单类型" clearable style="width: 100%;">
							<el-option v-for="item in workOrderTypeOptions" :key="item.value" :label="item.label"
								:value="item.value" />
						</el-select>
					</el-form-item>

					<!-- 查询按钮组 -->
					<div class="search-buttons">
						<el-button type="default" @click="onReset">重置</el-button>
						<el-button type="primary" @click="queryList">查询</el-button>
						<el-link type="primary" @click="toggleExpand" :underline="false" style="margin-left: 10px;">
							{{ isExpanded ? '收起' : '展开' }}
							<el-icon>
								<arrow-up v-if="isExpanded" />
								<arrow-down v-else />
							</el-icon>
						</el-link>
					</div>
				</div>
			</el-form>
		</div>
		<div class="cus-main" ref="mainRef">
			<div class="cus-list" v-loading="loading" ref="cusListRef">
				<div style="text-align: right;">
					<el-button type="success" plain @click="handleAdd">新增故障</el-button>
				</div>
				<el-table :data="listArr" class="cus-table">
					<el-table-column fixed align="center" type="index" label="序号" width="60" />
					<el-table-column fixed align="center" prop="name" label="故障名称" width="150">
						<template #default="scope">
							{{ scope.row.name || '-' }}
						</template>
					</el-table-column>
					<el-table-column align="center" prop="level" label="故障等级" width="100">
						<template #default="scope">
							{{ getFaultLevelLabel(scope.row.level) || '-' }}
						</template>
					</el-table-column>
					<el-table-column align="center" prop="workOrderType" label="工单类型" width="120">
						<template #default="scope">
							{{ getWorkOrderTypeLabel(scope.row.workOrderType) || '-' }}
						</template>
					</el-table-column>
					<el-table-column align="center" prop="code" label="故障码" width="120">
						<template #default="scope">
							<el-button link type="primary" @click="handleView(scope.row)" v-if="scope.row.workOrderType === 'fault'">
								查看
							</el-button>
							<span v-else>-</span>
						</template>
					</el-table-column>
					<el-table-column align="center" prop="description" label="描述" min-width="180" show-overflow-tooltip>
						<template #default="scope">
							{{ scope.row.description || '-' }}
						</template>
					</el-table-column>
					<el-table-column align="center" prop="status" label="状态" width="100">
						<template #default="scope">
							{{ scope.row.status == 1 ? '启用' : '禁用' }}
						</template>
					</el-table-column>
					<el-table-column align="center" prop="createdBy" label="创建人" width="120">
						<template #default="scope">
							{{ scope.row.createdBy || '-' }}
						</template>
					</el-table-column>
					<el-table-column align="center" prop="createdAt" label="创建时间" width="180">
						<template #default="scope">
							{{ scope.row.createdAt && scope.row.createdAt.replace('T', ' ') || '-' }}
						</template>
					</el-table-column>
					<el-table-column align="center" prop="updatedBy" label="修改人" width="120">
						<template #default="scope">
							{{ scope.row.updatedBy || '-' }}
						</template>
					</el-table-column>
					<el-table-column align="center" prop="updatedAt" label="修改时间" width="180">
						<template #default="scope">
							{{ scope.row.updatedAt && scope.row.updatedAt.replace('T', ' ') || '-' }}
						</template>
					</el-table-column>
					<el-table-column fixed="right" align="center" label="操作" width="160">
						<template #default="scope">
							<el-button link type="primary" @click="handleEdit(scope.row)">编辑</el-button>
							<el-button link type="primary" v-if="scope.row.status == 1"
								@click="toggleStatus(scope.row, 0)">禁用</el-button>
							<el-button link type="primary" v-else @click="toggleStatus(scope.row, 1)">启用</el-button>
							<el-button link type="danger" @click="handleDelete(scope.row)">删除</el-button>
						</template>
					</el-table-column>
				</el-table>
				<el-pagination class="cus-pages" v-if="total" background layout="sizes, prev, pager, next, ->, total"
					:page-sizes="[10, 20, 30]" :page-size="pagination.pageSize" :current-page="pagination.pageNum" :total="total"
					@size-change="changeSize" @current-change="changeCurrent" />
			</div>
		</div>
		<Operate v-model:visible="operateVisible" :data="operateData" @success="handleOperateSuccess" />
		<ViewFaultCode v-model:visible="viewVisible" :data="viewData" />
	</div>
</template>

<script setup>
import { ref, reactive, onMounted, computed } from 'vue';
import { useTablePagination } from '@/composables/useTablePagination';
import API from '@/api/maintainance';
import _ from 'lodash';
import { ArrowDown, ArrowUp } from '@element-plus/icons-vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import Operate from './components/operate.vue';
import ViewFaultCode from './components/viewFaultCode.vue';
import { useDictStore } from '@/stores/modules/dict';

const dictStore = useDictStore();

const isExpanded = ref(false);
const searchForm = reactive({
	name: '',
	level: '',
	code: '',
	manufacturer: '',
	deviceModel: '',
	workOrderType: ''
});

const {
	loading,
	listArr,
	total,
	pagination,
	getList,
	queryList,
	changeSize,
	changeCurrent
} = useTablePagination(
	API.getFaultCategoryPage,
	() => ({ ...searchForm }),
	{ manual: true }
);

const faultLevelOptions = computed(() => dictStore.getDictByType('fault_level'));
const manufacturerOptions = computed(() => dictStore.getDictByType('inverter_manufacturer'));
const workOrderTypeOptions = computed(() => dictStore.getDictByType('work_order_type'));
const operateVisible = ref(false);
const operateData = ref({});
const viewVisible = ref(false);
const viewData = ref({});


const getFaultLevelLabel = (value) => {
	const faultLevelMap = dictStore.getDictMapByType('fault_level');
	return faultLevelMap[value] || value;
};

const getWorkOrderTypeLabel = (value) => {
	const workOrderTypeMap = dictStore.getDictMapByType('work_order_type');
	return workOrderTypeMap[value] || value;
};

const onReset = () => {
	Object.assign(searchForm, {
		name: '',
		level: '',
		code: '',
		manufacturer: '',
		deviceModel: '',
		workOrderType: ''
	});
	queryList();
};

const toggleExpand = () => {
	isExpanded.value = !isExpanded.value;
};

const handleAdd = () => {
	operateData.value = {};
	operateVisible.value = true;
};

const handleView = (row) => {
	viewData.value = { ...row };
	viewVisible.value = true;
};

const handleEdit = (row) => {
	operateData.value = { ...row };
	operateVisible.value = true;
};

const handleOperateSuccess = () => {
	queryList();
};

const handleDelete = (row) => {
	ElMessageBox.confirm('确认要删除该故障吗？', '提示', {
		confirmButtonText: '确定',
		cancelButtonText: '取消',
		type: 'warning'
	}).then(() => {
		API.deleteFaultCategory({ id: row.id }).then(res => {
			if (res.data.success) {
				ElMessage.success('删除成功');
				queryList();
			} else {
				ElMessage.error(res.data.error || '删除失败');
			}
		}).catch(error => {
			ElMessage.error(error.message || '请求失败');
		});
	});
};

const toggleStatus = (row, status) => {
	const action = status === 1 ? '启用' : '禁用';
	ElMessageBox.confirm(`确认要${action}该故障分类吗？`, '提示', {
		confirmButtonText: '确定',
		cancelButtonText: '取消',
		type: 'warning'
	}).then(() => {
		API.updateFaultCategoryStatus({ id: row.id, status }).then(res => {
			if (res.data.success) {
				ElMessage.success(`${action}成功`);
				queryList();
			} else {
				ElMessage.error(res.data.error || `${action}失败`);
			}
		}).catch(error => {
			ElMessage.error(error.message || '请求失败');
		});
	});
}

onMounted(() => {
	getList();
	dictStore.fetchDict(['fault_level', 'inverter_manufacturer', 'work_order_type']);
});
</script>

<style lang="less" scoped>
@import '@/assets/style/_cus_header.less';
@import '@/assets/style/_cus_list.less';

.wrap {
	height: 100%;
}

.cus-header {
	margin-bottom: 0px;

	.form-item-grid {
		display: grid;
		grid-template-columns: repeat(3, 1fr);
		gap: 12px;
		width: 100%;

		/* 默认状态（收起）- 只显示前两个元素和按钮组 */
		.el-form-item:nth-child(n+3):not(:last-child) {
			display: none;
		}

		/* 展开状态 - 显示所有元素 */
		&.is-expanded {
			.el-form-item:nth-child(n+3):not(:last-child) {
				display: flex;
			}
		}
	}

	.search-buttons {
		display: flex;
		align-items: center;
		justify-content: flex-end;
		grid-column: -2 / -1;
	}

	.el-form-item {
		width: 100%;
		margin-bottom: 0px;

		.el-input,
		.el-select,
		.el-date-editor {
			width: 100%;
		}
	}
}

.cus-main {
	display: flex;
	flex-direction: column;
	flex: 1;
	overflow: hidden;

	>.el-button {
		margin-bottom: 10px;
	}

	.cus-list {
		display: flex;
		flex-direction: column;
		flex: 1;
		overflow: hidden;
		position: relative;

		// 表格自动填充剩余空间
		.cus-table {
			flex: 1;
			overflow: auto;
			height: 100%;
		}

		// 分页控件固定在底部
		.cus-pages {
			margin-top: 10px;
		}
	}
}
</style>
