<template>
	<div class="station-detail-page">
		<el-page-header class="cus-back" icon="ArrowLeft" @back="$router.go(-1)" />

		<!-- 电站信息 -->
		<el-card class="section-card">
			<template #header>
				<div class="card-header">
					<span>电站信息</span>
				</div>
			</template>
			<el-descriptions :column="2" border>
				<el-descriptions-item label="电站编码">{{ formData.stationCode || '-' }}</el-descriptions-item>
				<el-descriptions-item label="逆变器SN码">
					<el-button v-if="formData.inverterSn" link type="primary">
						{{ formData.inverterSn }}
					</el-button>
				</el-descriptions-item>
				<el-descriptions-item label="业主姓名">{{ formData.name || '-' }}</el-descriptions-item>
				<el-descriptions-item label="手机号">{{ formData.phone || '-' }}</el-descriptions-item>
				<el-descriptions-item label="电站模式">{{ detailMode[formData.mode] || '-' }}</el-descriptions-item>
				<el-descriptions-item label="关联资方">{{ pmSpecialFlag[formData.specialFlag] || '-' }}</el-descriptions-item>
				<el-descriptions-item label="所属分中心">{{ formData.subCenterName || '-' }}</el-descriptions-item>
				<el-descriptions-item label="服务商类别">{{ identityType[formData.opType] || '-' }}</el-descriptions-item>
				<el-descriptions-item label="运维商名称">{{ formData.opName || '-' }}</el-descriptions-item>
				<el-descriptions-item label="运维商业务类型">{{ businessType[formData.businessType] || '-' }}</el-descriptions-item>
				<el-descriptions-item label="是否质保期内">{{ isWarranty[formData.isWarranty] || '-' }}</el-descriptions-item>
				<el-descriptions-item label="区域">{{ (formData.provinceName || '') + (formData.cityName || '') +
					(formData.regionName
						|| '') || '-' }}</el-descriptions-item>
				<el-descriptions-item label="详细地址">{{ formData.address || '-' }}</el-descriptions-item>
			</el-descriptions>
		</el-card>

    <!-- 逆变器信息 -->
		<el-card v-for="item in inverterList" :key="item.inverterSn" class="section-card">
			<template #header>
				<div class="card-header">
					<span>逆变器{{ item.inverterSn }}</span>
				</div>
			</template>
			<el-descriptions :column="2" border>
				<el-descriptions-item label="逆变器SN码">
					{{ item.inverterSn || '-' }}
				</el-descriptions-item>
				<el-descriptions-item label="逆变器品牌">
					{{ item.brandName || '-' }}
				</el-descriptions-item>
				<el-descriptions-item label="逆变器型号">
          {{ item.inverterSn || '-'  }}
				</el-descriptions-item>
				<el-descriptions-item label="逆变器图片">
					<template v-if="item.imageUrl">
            <el-image
              style="width: 100px; height: 100px;"
              :src="item.imageUrl"
              :preview-src-list="[item.imageUrl]" :initial-index="0" fit="cover"
              preview-teleported />
          </template>
          <span v-else>-</span>
				</el-descriptions-item>
        <el-descriptions-item label="装机容量">
          {{ item.power || '-'  }}
				</el-descriptions-item>
        <el-descriptions-item label="实时功率">
          {{ item.pac || '-'  }}
				</el-descriptions-item>
        <el-descriptions-item label="日发电量(kWh)">
          {{ item.elecDay || '-'  }}
				</el-descriptions-item>
        <el-descriptions-item label="月发电量(kWh)">
          {{ item.elecMonth || '-'  }}
				</el-descriptions-item>
        <el-descriptions-item label="年发电量(kWh)">
          {{ item.elecYear || '-'  }}
				</el-descriptions-item>
        <el-descriptions-item label="累计发电量(kWh)">
          {{ item.elecTotal || '-'  }}
				</el-descriptions-item>
        <el-descriptions-item label="日等效小时数">
          {{ item.dailyEquivalentHours || '-'  }}
				</el-descriptions-item>
        <el-descriptions-item label="逆变器状态">
          {{ item.inveterState || '-'  }}
				</el-descriptions-item>
        <el-descriptions-item label="组串数量">
          {{ item.stringDetailParsed ? item.stringDetailParsed.length : '0'  }}
				</el-descriptions-item>
        <el-descriptions-item label="组串总块数">
          {{ getTotalPvCount(item.stringDetailParsed) }}
				</el-descriptions-item>
        <el-descriptions-item label="组串详情" :span="2">
          <div>
            <div v-if="item.stringDetailParsed && item.stringDetailParsed.length > 0">
              <div v-for="mppt in item.stringDetailParsed" :key="mppt.name" style="margin-bottom: 5px;">
                <p style="margin: 0; padding: 0;">
                  <strong>{{ mppt.name }}</strong>
                  (总数: {{ mppt.total || 0 }})
                  <span v-if="mppt.pv && mppt.pv.length > 0">: {{ mppt.pv.map(p => `${p.name}: ${p.total || 0}`).join(', ') }}</span>
                </p>
              </div>
            </div>
            <span v-else>-</span>
            <el-button link type="primary" style="margin-left: 8px;" @click="onEditStringDetail(item)">
              {{ item.stringDetailParsed && item.stringDetailParsed.length > 0 ? '编辑' : '新增' }}
            </el-button>
          </div>
				</el-descriptions-item>
			</el-descriptions>
      <el-tabs v-model="item.activeTab" type="border-card" class="section-card" style="margin-top: 20px" @tab-change="onTabChange(item)">
        <el-tab-pane label="功率/温度" name="power-temp">
          <el-date-picker
            v-model="item.powerTempQueryDate"
            type="date"
            placeholder="选择日期"
            value-format="YYYY-MM-DD"
            @change="onDateChange(item, 'power-temp')"
            style="width: 150px; margin-bottom: 20px;"
          />
          <div :id="'powerTempChart-' + item.inverterSn" style="width: 100%; height: 400px"></div>
        </el-tab-pane>
        <el-tab-pane label="MPPT" name="mppt">
          <el-date-picker
            v-model="item.mpptQueryDate"
            type="date"
            placeholder="选择日期"
            value-format="YYYY-MM-DD"
            @change="onDateChange(item, 'mppt')"
            style="width: 150px; margin-bottom: 20px;"
          />
          <div :id="'mpptChart-' + item.inverterSn" style="width: 100%; height: 400px"></div>
        </el-tab-pane>
      </el-tabs>
		</el-card>
    <WorkOrderList v-if="formData.stationCode" :stationCode="formData.stationCode" />
    <StringDetailEditDialog
      v-if="editingInverter"
      v-model:visible="isDialogVisible"
      :data="editingInverter.stringDetailParsed"
      @submit="handleStringDetailUpdate"
      ref="dialogRef"
    />
	</div>
</template>

<script setup>
import { ref, onMounted, computed, shallowRef, onUnmounted, nextTick } from 'vue'
import { ElMessage } from 'element-plus'
import { useRoute, useRouter } from 'vue-router'
import * as echarts from 'echarts';
import API from '@/api/maintainance'
import _D from '@/edata/_osp_data'
import _ from 'lodash';
import { useDictStore } from '@/stores/modules/dict';
import WorkOrderList from './components/WorkOrderList.vue';
import StringDetailEditDialog from './components/StringDetailEditDialog.vue';

const route = useRoute()
const router = useRouter()

const pmSpecialFlag = _D.pmSpecialFlag;
const businessType = _D.businessType;
const isWarranty = _D.isWarranty;
const identityType = _D.identityType;
const detailMode = _D.detailMode;
const inverterList = ref([])
let chartInstances = [];

onUnmounted(() => {
	chartInstances.forEach(chart => {
		chart.dispose();
	});
	chartInstances = [];
});

const getTotalPvCount = (parsedDetails) => {
	if (!parsedDetails || !Array.isArray(parsedDetails)) return 0;
	return parsedDetails.reduce((sum, mppt) => {
		if (!mppt.pv || !Array.isArray(mppt.pv)) return sum;
		const mpptTotal = mppt.pv.reduce((pvSum, pv) => pvSum + (pv.total || 0), 0);
		return sum + mpptTotal;
	}, 0);
};

const isDialogVisible = ref(false);
const editingInverter = ref(null);
const dialogRef = ref(null);

const onEditStringDetail = (inverter) => {
	console.log('onEditStringDetail: 打开编辑对话框', {
		inverterSn: inverter.inverterSn,
		stringDetailParsed: inverter.stringDetailParsed,
		originalStringDetailParsed: inverter.originalStringDetailParsed
	});
	editingInverter.value = inverter;
	isDialogVisible.value = true;
};

const handleStringDetailUpdate = async (updatedData) => {
  if (!editingInverter.value) return;

  const inverter = editingInverter.value;
  const stringDetail = JSON.stringify(updatedData);

  try {
    const payload = {
      inverterSn: inverter.inverterSn,
      stringDetail: stringDetail,
    };
    // TODO: 待后端提供接口, 例如: API.updateStationInverterStringDetail(payload)
    // const res = await API.updateStationInverterStringDetail(payload);

    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 500));
    const res = { success: true };

    if (res.success) {
      ElMessage.success('更新成功');
      console.log('handleStringDetailUpdate: 保存成功，更新数据', {
        inverterSn: inverter.inverterSn,
        updatedData: updatedData,
        beforeUpdate: {
          stringDetailParsed: inverter.stringDetailParsed,
          originalStringDetailParsed: inverter.originalStringDetailParsed
        }
      });

      // 更新数据
      inverter.stringDetailParsed = _.cloneDeep(updatedData);
      inverter.originalStringDetailParsed = _.cloneDeep(updatedData);

      console.log('handleStringDetailUpdate: 数据更新完成', {
        afterUpdate: {
          stringDetailParsed: inverter.stringDetailParsed,
          originalStringDetailParsed: inverter.originalStringDetailParsed
        }
      });

      // 延迟关闭对话框，确保数据更新完成
      await nextTick();
      isDialogVisible.value = false;
      editingInverter.value = null;
    } else {
      ElMessage.error(res.error || '更新失败');
    }
  } catch (error) {
    console.error('更新组串详情失败:', error);
    ElMessage.error('更新组串详情时发生错误');
  } finally {
    dialogRef.value?.resetSubmitting();
  }
};

const initChart = (elementId, title, seriesData, yAxis, xAxisData) => {
	const chartDom = document.getElementById(elementId);
	if (!chartDom) return;

	let myChart = echarts.getInstanceByDom(chartDom);
	if (!myChart) {
		myChart = echarts.init(chartDom);
		chartInstances.push(myChart);
	}

	const option = {
		title: {
			text: title,
			left: 'center'
		},
		tooltip: {
			trigger: 'axis',
			axisPointer: {
				type: 'cross'
			}
		},
		legend: {
			data: seriesData.map(s => s.name),
			top: 30
		},
		grid: {
			right: '10%'
		},
		xAxis: {
			type: 'category',
			data: xAxisData,
			axisPointer: {
				type: 'shadow'
			}
		},
		yAxis: yAxis,
		dataZoom: [
			{
				type: 'slider',
				start: 0,
				end: 100,
			},
		],
		series: seriesData
	};
	myChart.setOption(option, true);
};

const onTabChange = (inverter) => {
	nextTick(() => {
		if (inverter.activeTab === 'power-temp') {
			const xAxisData = inverter.inverterData.map(item => item.dataTimestamp);
			const powerData = inverter.inverterData.map(item => item.pac);
			const tempData = inverter.inverterData.map(item => item.inverterTemperature);
			initChart(
				'powerTempChart-' + inverter.inverterSn,
				'功率/温度曲线',
				[{
					name: '功率',
					type: 'line',
					smooth: true,
					data: powerData,
					yAxisIndex: 0,
				},
				{
					name: '温度',
					type: 'line',
					smooth: true,
					data: tempData,
					yAxisIndex: 1,
				}],
				[
					{
						type: 'value',
						name: '功率(W)',
						position: 'left',
						axisLine: {
							show: true,
						},
					},
					{
						type: 'value',
						name: '温度(℃)',
						position: 'right',
						axisLine: {
							show: true,
						},
					}
				],
				xAxisData
			);
		} else if (inverter.activeTab === 'mppt') {
			const series = [];
			let xAxisData = [];
			if (inverter.mpptData && Object.keys(inverter.mpptData).length > 0) {
				const mpptNames = Object.keys(inverter.mpptData);

				mpptNames.forEach(mpptName => {
					const mpptGroupData = inverter.mpptData[mpptName];
					if (mpptGroupData.length > 0) {
						series.push({
							name: mpptName,
							type: 'line',
							smooth: true,
							data: mpptGroupData.map(item => item.power)
						});
					}
				});

				if (series.length > 0) {
					const firstMpptGroup = inverter.mpptData[mpptNames[0]];
					if (firstMpptGroup) {
						xAxisData = firstMpptGroup.map(item => item.dataTimestamp);
					}
				}
			}
			initChart(
				`mpptChart-${inverter.inverterSn}`,
				'MPPT 功率曲线',
				series,
				{
					type: 'value',
					name: '功率(W)'
				},
				xAxisData
			);
		}
	});
};

const formData = ref({
	address: '',
	auditMode: '',
	auditUnit: '',
	businessType: '',
	cityId: '',
	cityName: '',
	closeReason: '',
	closeTime: '',
	configCheckItems: [],
	configId: '',
	createdAt: '',
	createdBy: '',
	deadline: '',
	devices: {},
	dispatchAuditPermission: '',
	dispatchMode: '',
	dispatchUnit: '',
	dispatched: null,
	faultDescription: '',
	faultInfos: [],
	finishFlag: '',
	finishTime: '',
	firstAuditResult: '',
	handleCheckItems: [],
	handleTime: '',
	handler: '',
	id: '',
	inverterSn: '',
	isWarranty: null,
	opCode: '',
	opMemberId: '',
	opName: '',
	opType: '',
	orderCode: '',
	orderName: '',
	orderSource: '',
	orderStatus: '',
	orderType: '',
	overTime: null,
	processes: [],
	provinceId: '',
	provinceName: '',
	regionId: '',
	regionName: '',
	remark: '',
	secondAuditResult: '',
	sparePartApplyNo: '',
	sparePartAuditPassTime: '',
	sparePartAuditStatus: '',
	specialFlag: '',
	stationCode: '',
	stationMode: '',
	stationName: '',
	stationPhone: '',
	streetId: '',
	streetName: '',
	subCenterName: '',
})

const loadPowerTempChartData = async (inverter) => {
	const params = {
		inverterSn: inverter.inverterSn,
		date: inverter.powerTempQueryDate,
	};
	const inverterDataRes = await API.getStationInverterData(params);
	inverter.inverterData = inverterDataRes.success ? inverterDataRes.result : [];
};

const loadMpptChartData = async (inverter) => {
	const params = {
		inverterSn: inverter.inverterSn,
		date: inverter.mpptQueryDate,
	};
	const mpptDataRes = await API.getStationInverterMpptData(params);
	const rawMpptData = mpptDataRes.success ? mpptDataRes.result : [];
	if (rawMpptData.length > 0) {
		const grouped = _.groupBy(rawMpptData, 'mpptName');
		for (const mpptName in grouped) {
			grouped[mpptName].sort((a, b) => new Date(a.dataTimestamp) - new Date(b.dataTimestamp));
		}
		inverter.mpptData = grouped;
	} else {
		inverter.mpptData = {};
	}
};

async function onDateChange(item, chartType) {
	if (chartType === 'power-temp') {
		await loadPowerTempChartData(item);
	} else if (chartType === 'mppt') {
		await loadMpptChartData(item);
	}
	onTabChange(item);
}

const getDetail = async () => {
	const stationCode = route.query.stationCode
	if (!stationCode) {
		ElMessage.error('缺少工单标识')
		router.back()
		return
	}
	formData.value.stationCode = stationCode

	try {
		const res = await API.getStationByStationCode({ stationCode })

		if (res.success) {
			Object.assign(formData.value, res.result)
		} else {
			ElMessage.error(res.error || '获取详情失败')
		}

		const inverterListRes = await API.getStationInverterList({ stationCode });
		if (inverterListRes.success && inverterListRes.result) {
			const today = new Date().toISOString().slice(0, 10);
			const inverters = inverterListRes.result.map(inv => {
				let stringDetailParsed, originalStringDetailParsed;
				try {
					// 确保stringDetail存在且为有效JSON，否则使用空数组
					const details = JSON.parse(inv.stringDetail || '[]');
					stringDetailParsed = Array.isArray(details) ? details : [];
				} catch (e) {
					console.error('解析组串详情失败:', e);
					stringDetailParsed = [];
				}
				originalStringDetailParsed = _.cloneDeep(stringDetailParsed);

				return {
					...inv,
					activeTab: 'power-temp',
					powerTempQueryDate: today,
					mpptQueryDate: today,
					inverterData: [],
					mpptData: {},
					stringDetailParsed,
					originalStringDetailParsed
				};
			});

			inverterList.value = inverters;

			for (const inverter of inverterList.value) {
				await Promise.all([loadPowerTempChartData(inverter), loadMpptChartData(inverter)]);
				onTabChange(inverter);
				const elecDataRes = await API.getStationInverterElecData({ inverterSn: inverter.inverterSn });
				inverter.elecData = elecDataRes.success ? elecDataRes.result : null;
			}
		}
	} catch (error) {
		console.error('获取详情失败:', error)
		ElMessage.error('获取详情数据时发生错误')
	}
}

const dictStore = useDictStore();

onMounted(() => {
	getDetail()
	dictStore.fetchDict([
		'close_order_reason',
	]);
})

</script>

<style lang="less" scoped>
@import '@/assets/style/_cus_view.less';

.station-detail-page {
	padding: 20px;
	background-color: #f8f9fa;
	height: 100%;
	box-sizing: border-box;
	overflow: auto;

	.section-card {
		margin-bottom: 20px;
		border-radius: 4px;
		box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);

		.card-header {
			display: flex;
			align-items: center;
			font-size: 16px;
			font-weight: 600;
			color: #303133;
		}

		:deep(.el-card__header) {
			background-color: #ffffff;
			padding: 12px 20px;
			border-bottom: 1px solid #e9ecef;
		}

		:deep(.el-card__body) {
			padding: 20px;
		}

		// Descriptions 样式调整
		:deep(.el-descriptions__label) {
			font-weight: normal;
			color: #6c757d;
			background-color: #f8f9fa;
		}

		:deep(.el-descriptions__content) {
			color: #212529;
		}

		:deep(.el-descriptions__cell) {
			padding: 8px 12px;
		}
	}

	.info-label {
		color: #6c757d;
		margin-bottom: 8px;
		font-size: 14px;
	}

	.image-slot {
		display: flex;
		justify-content: center;
		align-items: center;
		width: 100%;
		height: 100%;
		background: #f5f7fa;
		color: #c0c4cc;
		font-size: 14px;
	}
}
</style>
