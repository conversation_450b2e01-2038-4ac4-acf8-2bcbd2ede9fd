<template>
	<div class="wrap">
		<div class="cus-header">
			<el-form :model="formSearch" label-width="100px">
				<!-- 基础查询条件 -->
				<div class="form-item-grid" :class="[isExpanded ? 'is-expanded' : '']">
					<el-form-item label="电站编码">
						<el-input v-model="formSearch.stationCode" placeholder="输入电站编码" clearable />
					</el-form-item>
					<el-form-item label="业主姓名">
						<el-input v-model="formSearch.name" placeholder="输入业主姓名" clearable />
					</el-form-item>
					<el-form-item label="联系方式">
						<el-input v-model="formSearch.phone" placeholder="输入联系方式" clearable />
					</el-form-item>

					<!-- 展开的查询条件 -->
					<el-form-item label="分中心">
						<el-select v-model="formSearch.subCenterCode" placeholder="请选择分中心" clearable>
							<el-option v-for="(item, val) in subCenterList" :label="item.label" :value="item.value" :key="val" />
						</el-select>
					</el-form-item>
					<el-form-item label="运维服务商">
						<el-input v-model="formSearch.opName" placeholder="输入运维服务商名称" clearable />
					</el-form-item>
					<el-form-item label="模式">
						<el-select v-model="formSearch.mode" placeholder="选择模式" clearable style="width: 100%;">
							<el-option v-for="item in pmDetailMode" :key="item.value" :label="item.label" :value="item.value" />
						</el-select>
					</el-form-item>

					<!-- 查询按钮组 -->
					<div class="search-buttons">
						<el-button type="default" @click="onReset">重置</el-button>
						<el-button type="primary" @click="queryList">查询</el-button>
						<el-link type="primary" @click="toggleExpand" :underline="false" style="margin-left: 10px;">
							{{ isExpanded ? '收起' : '展开' }}
							<el-icon>
								<arrow-up v-if="isExpanded" />
								<arrow-down v-else />
							</el-icon>
						</el-link>
					</div>
				</div>
			</el-form>
		</div>
		<div class="cus-main" ref="mainRef">
			<div class="cus-list" v-loading="loading" ref="cusListRef">
				<el-table :data="listArr" class="cus-table">
					<el-table-column fixed align="center" type="index" label="序号" width="60" />
					<el-table-column align="center" prop="stationCode" label="电站编码" width="150">
						<template #default="scope">
							{{ scope.row.stationCode || '-' }}
						</template>
					</el-table-column>
					<el-table-column align="center" prop="name" label="业主姓名" width="120">
						<template #default="scope">
							{{ scope.row.name || '-' }}
						</template>
					</el-table-column>
					<el-table-column align="center" prop="subCenterName" label="分中心" width="120" show-overflow-tooltip>
						<template #default="scope">
							{{ scope.row.subCenterName || '-' }}
						</template>
					</el-table-column>
					<el-table-column align="center" prop="phone" label="联系方式" width="120">
						<template #default="scope">
							{{ scope.row.phone || '-' }}
						</template>
					</el-table-column>
					<el-table-column align="center" prop="address" label="详细地址" min-width="180" show-overflow-tooltip>
						<template #default="scope">
							{{ scope.row.address || '-' }}
						</template>
					</el-table-column>
					<el-table-column align="center" prop="provinceName" label="省份" width="120" show-overflow-tooltip>
						<template #default="scope">
							{{ scope.row.provinceName || '-' }}
						</template>
					</el-table-column>
					<el-table-column align="center" prop="cityName" label="城市" width="120" show-overflow-tooltip>
						<template #default="scope">
							{{ scope.row.cityName || '-' }}
						</template>
					</el-table-column>
					<el-table-column align="center" prop="mode" label="模式" width="100">
						<template #default="scope">
							{{ detailMode[scope.row.mode] || '-' }}
						</template>
					</el-table-column>
					<el-table-column align="center" prop="opName" label="运维服务商" width="150" show-overflow-tooltip>
						<template #default="scope">
							{{ scope.row.opName || '-' }}
						</template>
					</el-table-column>
					<el-table-column align="center" prop="power" label="装机功率(kW)" width="120">
						<template #default="scope">
							{{ scope.row.power || '-' }}
						</template>
					</el-table-column>
					<el-table-column align="center" prop="createdAt" label="创建日期" width="180">
						<template #default="scope">
							{{ scope.row.createdAt && scope.row.createdAt.replace('T', ' ') || '-' }}
						</template>
					</el-table-column>
					<el-table-column fixed="right" align="center" label="操作" width="100">
						<template #default="scope">
							<el-button link type="primary" @click="handleView(scope.row)">查看</el-button>
						</template>
					</el-table-column>
				</el-table>
				<el-pagination class="cus-pages" v-if="total" background layout="sizes, prev, pager, next, ->, total"
					:page-sizes="[10, 20, 30]" :page-size="pagination.pageSize" :current-page="pagination.pageNum" :total="total"
					@size-change="changeSize" @current-change="changeCurrent" />
			</div>
		</div>
	</div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue';
import API from '@/api/maintainance';
import { ArrowDown, ArrowUp } from '@element-plus/icons-vue';
import { useTablePagination } from '@/composables/useTablePagination';
import _D from '@/edata/_osp_data';
import { useRouter } from 'vue-router'

const subCenterList = _D.subCenterList;
const detailMode = _D.detailMode;
const pmDetailMode = _D.pmDetailMode;
const isExpanded = ref(false);
const formSearch = reactive({
	name: '',
	phone: '',
	stationCode: '',
	opName: '',
	mode: ''
});

const {
	loading,
	listArr,
	total,
	pagination,
	getList,
	queryList,
	changeSize,
	changeCurrent,
} = useTablePagination(
	API.getStationPage,
	() => formSearch,
	{ manual: true }
);

const onReset = () => {
	formSearch.name = '';
	formSearch.phone = '';
	formSearch.stationCode = '';
	formSearch.opName = '';
	formSearch.mode = '';
	queryList();
};

const toggleExpand = () => {
	isExpanded.value = !isExpanded.value;
};

const router = useRouter()
const handleView = (row) => {
	router.push({
		path: '/maintainance/station/detail',
		query: {
			stationCode: row.stationCode,
		}
	});
}

onMounted(async () => {
	getList();
});
</script>

<style lang="less" scoped>
@import '@/assets/style/_cus_header.less';
@import '@/assets/style/_cus_list.less';

.wrap {
	height: 100%;
}

.cus-header {
	margin-bottom: 0px;

	.form-item-grid {
		display: grid;
		grid-template-columns: repeat(3, 1fr);
		/* 每行3个查询条件 */
		gap: 12px;
		width: 100%;

		/* 默认状态（收起）- 根据实际需要调整显示的查询条件数量 */
		.el-form-item:nth-child(n+3):not(:last-child) {
			/* 假设默认显示前3个 */
			display: none;
		}

		/* 展开状态 - 显示所有元素 */
		&.is-expanded {
			.el-form-item:nth-child(n+3):not(:last-child) {
				display: flex;
			}
		}
	}

	.search-buttons {
		display: flex;
		align-items: center;
		justify-content: flex-end;
		grid-column: -2 / -1;
		/* 确保按钮组在最后一列 */
	}

	.el-form-item {
		width: 100%;
		margin-bottom: 0px;

		.el-input,
		.el-select,
		.el-date-editor {
			width: 100%;
		}
	}
}

.cus-main {
	display: flex;
	flex-direction: column;
	flex: 1;
	overflow: hidden;

	.cus-list {
		display: flex;
		flex-direction: column;
		flex: 1;
		overflow: hidden;
		position: relative;

		.cus-table {
			flex: 1;
			overflow: auto;
			height: 100%;
		}

		.cus-pages {
			margin-top: 10px;
		}
	}
}
</style>