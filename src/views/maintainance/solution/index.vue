<template>
  <div class="cus-container">
    <div class="cus-tree" ref="treeContainer">
      <div class="button-group">
        <el-button type="primary" @click="addCategory">添加</el-button>
        <el-button type="primary" @click="editCategory">编辑</el-button>
        <el-button type="danger" @click="deleteCategory">删除</el-button>
      </div>
      <div>
        <el-input v-model="filterText" placeholder="输入关键字过滤" clearable>
          <template #append>
            <el-button icon="search" />
          </template>
        </el-input>
      </div>
      <el-tree-v2 ref="myTree" node-key="id" :expand-on-click-node="false" highlight-current check-on-click-node
        :data="solutionTypeTree" :current-node-key="node" :filter-method="filterNode" @node-click="nodeChange"
        @node-expand="handleNodeExpand" @node-collapse="handleNodeCollapse" :height="treeHeight" />
    </div>
    <div class="wrap">
      <Operate v-model:visible="operateVisible" :data="operateData" @success="handleSuccess" />
      <OperateCategory v-model:visible="categoryVisible" :data="categoryData" @success="handleCategorySuccess" />
      <div class="cus-header">
        <el-form :model="formSearch" label-width="80px">
          <!-- 基础查询条件 -->
          <div class="form-item-grid">
            <el-form-item label="方案名称">
              <el-input v-model="formSearch.name" clearable placeholder="输入方案名称" />
            </el-form-item>

            <el-form-item label="创建人">
              <el-input v-model="formSearch.createdBy" clearable placeholder="输入创建人" />
            </el-form-item>

            <!-- 查询按钮组 -->
            <div class="search-buttons">
              <el-button type="default" @click="onReset">重置</el-button>
              <el-button type="primary" @click="queryList">查询</el-button>
            </div>
          </div>
        </el-form>
      </div>
      <div class="cus-main" ref="mainRef">
        <div class="cus-list" v-loading="loading" ref="cusListRef">
          <div style="text-align: right;">
            <el-button type="success" plain @click="addSolution">新增</el-button>
          </div>
          <el-table :data="listArr" class="cus-table">
            <el-table-column align="center" prop="name" label="方案名称" width="180">
              <template #default="scope">
                {{ scope.row.name || '-' }}
              </template>
            </el-table-column>
            <el-table-column align="center" prop="categoryName" label="所属分类" width="150">
              <template #default="scope">
                {{ scope.row.categoryName || '-' }}
              </template>
            </el-table-column>
            <el-table-column align="center" prop="description" label="描述" min-width="200" show-overflow-tooltip>
              <template #default="scope">
                {{ scope.row.description || '-' }}
              </template>
            </el-table-column>
            <el-table-column align="center" prop="createdBy" label="创建人" width="120">
              <template #default="scope">
                {{ scope.row.createdBy || '-' }}
              </template>
            </el-table-column>
            <el-table-column align="center" prop="createdAt" label="创建时间" width="180">
              <template #default="scope">
                {{ scope.row.createdAt && scope.row.createdAt.replace('T', ' ') || '-' }}
              </template>
            </el-table-column>
            <el-table-column align="center" prop="updatedBy" label="修改人" width="120">
              <template #default="scope">
                {{ scope.row.updatedBy || '-' }}
              </template>
            </el-table-column>
            <el-table-column align="center" prop="updatedAt" label="修改时间" width="180">
              <template #default="scope">
                {{ scope.row.updatedAt && scope.row.updatedAt.replace('T', ' ') || '-' }}
              </template>
            </el-table-column>
            <el-table-column fixed="right" align="center" label="操作" width="160">
              <template #default="scope">
                <el-button link type="primary" @click="handleEdit(scope)">编辑</el-button>
                <el-button link type="danger" @click="handleDelete(scope)">删除</el-button>
              </template>
            </el-table-column>
          </el-table>
          <el-pagination class="cus-pages" v-if="total > 0" background layout="sizes, prev, pager, next, ->, total"
            :page-sizes="[10, 20, 30]" :page-size="pagination.pageSize" :current-page="pagination.pageNum"
            :total="total" @size-change="changeSize" @current-change="changeCurrent" />
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, onUnmounted, nextTick, watch } from 'vue';
import _ from 'lodash';
import { useRoute } from 'vue-router';
import { useTablePagination } from '@/composables/useTablePagination'; // 导入组合式函数
import { ElMessage, ElMessageBox } from 'element-plus';
import Operate from './components/operate.vue';
import OperateCategory from './components/operateCategory.vue';
import API from '@/api/maintainance';
import { useDictStore } from '@/stores/modules/dict';

const route = useRoute();
const dictStore = useDictStore(); // 2. 实例化 Store

const operateVisible = ref(false);
const operateData = ref({});
const categoryVisible = ref(false);
const categoryData = ref({});
// formSearch 不再包含 pageNum 和 pageSize，它们由 useTablePagination 管理
const formSearch = reactive({
  name: '',
  createdBy: '',
  solutionType: null, // 确保初始状态包含所有可能的查询字段
  categoryId: null,
});
// 使用组合式函数管理表格和分页状态
const {
  loading,
  listArr,
  total,
  pagination, // 获取分页参数对象
  getList,
  queryList,
  changeSize,
  changeCurrent,
} = useTablePagination(
  API.getSolutionPage, // 传递 API 函数
  () => formSearch, // 传递一个返回当前 formSearch 值的函数
  { manual: true } // 设置为手动加载，将在 onMounted 中调用 getList
);

const limits = ref(route.meta.limits);
const solutionTypeTree = ref([]);
const node = ref(null);
const myTree = ref(null);
const treeContainer = ref(null);
const treeHeight = ref(200);
const expandedKeys = ref([]);
const filterText = ref('');
const filterNode = (value, data) => {
  if (!value) return true;
  return data.label.toLowerCase().includes(value.toLowerCase());
};

// 计算树的高度
const calculateTreeHeight = () => {
  if (!treeContainer.value) return;
  const containerHeight = treeContainer.value.clientHeight;
  const otherElementsHeight = 100;
  const calculatedHeight = containerHeight - otherElementsHeight - 20; // 20px作为边距
  treeHeight.value = Math.max(calculatedHeight, 100);
};

watch(filterText, (val) => {
  myTree.value.filter(val);
});


const onReset = () => {
  // 只重置 formSearch 中的非分页字段
  formSearch.name = '';
  formSearch.createdBy = '';
  // 调用组合式函数提供的 queryList，它会重置分页并获取数据
  queryList();
};
const addSolution = () => {
  operateData.value = {};
  operateVisible.value = true;
};

const handleSuccess = () => {
  getList();
};

const addCategory = () => {
  categoryData.value = { };
  categoryVisible.value = true;
};

const editCategory = () => {
  const currentNode = myTree.value?.getCurrentNode();
  if (!currentNode || currentNode.children?.length > 0) {
    ElMessage.warning('请先选择要编辑的分类');
    return;
  }
  categoryData.value = { ...currentNode };
  categoryVisible.value = true;
};

const handleCategorySuccess = () => {
  dictStore.clearDict('solution_type');
  getSolutionTypeDict()
};

const deleteCategory = () => {
  const currentNode = myTree.value?.getCurrentNode();
  if (!currentNode || currentNode.children?.length > 0) {
    ElMessage.warning('请先选择要删除的方案分类');
    return;
  }
  ElMessageBox.confirm('确认要删除该方案类型吗？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning',
  }).then(() => {
    API.deleteSolutionCategory(currentNode.id).then((res) => {
      if (res.data.success) {
        ElMessage.success('删除成功');
        getSolutionTypeDict();
      } else {
        ElMessage.error(res.data.error || '删除失败');
      }
    }).catch(() => {
      ElMessage.error('删除失败');
    });
  }).catch(() => { });
};

const handleNodeExpand = (data) => {
  if (!expandedKeys.value.includes(data.id)) {
    expandedKeys.value.push(data.id);
  }
};

const handleNodeCollapse = (data) => {
  expandedKeys.value = expandedKeys.value.filter(key => key !== data.id);
};

const nodeChange = (data) => {
  if (node.value === data.id) {
    node.value = null;
    formSearch.solutionType = null
    formSearch.categoryId = null
    myTree.value.setCurrentKey(null);
  } else {
    node.value = data.id;
    if (data.type === 'solutionType') {
      formSearch.solutionType = data.id;
      formSearch.categoryId = null;
    } else {
      formSearch.solutionType = null;
      formSearch.categoryId = data.id;
    }
  }
  getList();
};

const getSolutionTypeDict = async () => {
  if (myTree.value?.getExpandedKeys) {
    expandedKeys.value = myTree.value.getExpandedKeys();
  }

  await dictStore.fetchDict('solution_type');

  const solutionTypeDictData = dictStore.getDictByType('solution_type');

  const listRes = await API.getSolutionCategoryList();

  // 检查数据源并构建树
  if (solutionTypeDictData && solutionTypeDictData.length > 0 && listRes.success && listRes.result) {
    solutionTypeTree.value = solutionTypeDictData.map(item => {
      return {
        label: item.label,
        id: item.value,
        type: 'solutionType',
        children: listRes.result.filter(item2 => item2.solutionType === item.value).map(item2 => {
          return {
            label: item2.name,
            type: 'category',
            ...item2
          }
        })
      }
    });
  } else {
    solutionTypeTree.value = [];
    if (!listRes.success) {
      ElMessage.error(`获取方案分类列表失败: ${listRes.error || listRes.message}`);
    }
  }

  nextTick(() => {
    if (myTree.value && expandedKeys.value.length > 0) {
      myTree.value.setExpandedKeys(expandedKeys.value);
    }
  });
}

const handleEdit = (scope) => {
  operateData.value = { ...scope.row };
  operateVisible.value = true;
};

const handleDelete = (scope) => {
  ElMessageBox.confirm('确认要删除该方案吗？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning',
  }).then(() => {
    API.deleteSolution({ id: scope.row.id }).then(response => {
      if (response.data.success) {
        ElMessage.success('删除成功');
        getList();
      } else {
        ElMessage.error(response.data.error || '删除失败');
      }
    }).catch(error => {
      ElMessage.error(error.message || '请求失败');
    });
  }).catch(() => {
  });
};

const addResizeListener = () => {
  window.addEventListener('resize', _.debounce(calculateTreeHeight, 200));
};

const removeResizeListener = () => {
  window.removeEventListener('resize', calculateTreeHeight);
};

onMounted(() => {
  getSolutionTypeDict();
  getList();

  nextTick(() => {
    calculateTreeHeight();
    addResizeListener();
  });
});

onUnmounted(() => {
  removeResizeListener();
});
</script>

<style lang="less" scoped>
@import '@/assets/style/_cus_header.less';
@import '@/assets/style/_cus_list.less';

.wrap {
  height: 100%;
  flex: 1;
  overflow: hidden;
}

.cus-container {
  display: flex;
  gap: 20px;
  height: 100%;
  width: 100%;

  .button-group {
    display: flex;
    justify-content: space-between;

    >* {
      flex: 1
    }
  }
}

.cus-tree {
  width: 20%;
  min-width: 230px;
  border-radius: 6px;
  box-sizing: border-box;
  padding: 12px;
  background-color: var(--el-bg-color);
  height: 100%;
  overflow: hidden;

  display: flex;
  flex-direction: column;
  gap: 10px;

  .el-tree,
  .el-tree-v2 {
    flex: 1;
    max-height: 100%;
    overflow: auto;
  }
}

.cus-header {
  margin-bottom: 0px;

  .form-item-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 12px;
    width: 100%;

    /* 默认状态（收起）- 只显示前两个元素和按钮组 */
    .el-form-item:nth-child(n+3):not(:last-child) {
      display: none;
    }

    /* 展开状态 - 显示所有元素 */
    &.is-expanded {
      .el-form-item:nth-child(n+3):not(:last-child) {
        display: flex;
      }
    }
  }

  .search-buttons {
    display: flex;
    align-items: center;
    justify-content: flex-end;
    grid-column: -2 / -1;
  }

  .el-form-item {
    width: 100%;
    margin-bottom: 0px;

    .el-input,
    .el-select,
    .el-date-editor {
      width: 100%;
    }
  }
}

.cus-main {
  display: flex;
  flex-direction: column;
  flex: 1;
  overflow: hidden;

  >.el-button {
    margin-bottom: 10px;
  }

  .cus-list {
    display: flex;
    flex-direction: column;
    flex: 1;
    overflow: hidden;
    position: relative;

    .cus-table {
      flex: 1;
      overflow: auto;
      height: 100%;
    }

    .cus-pages {
      margin-top: 10px;
    }
  }
}
</style>
