<template>
  <el-dialog :title="form.id ? '编辑方案' : '新增方案'" v-model="dialogVisible" width="600px" :close-on-click-modal="false"
    @close="handleClose" destroy-on-close>
    <el-form ref="formRef" :model="form" :rules="rules" label-width="100px">
      <el-form-item label="方案名称" prop="name">
        <el-input v-model="form.name" placeholder="请输入方案名称" />
      </el-form-item>
      <el-form-item label="所属分类" prop="solutionType">
        <el-select style="width: 100%" v-model="form.solutionType" placeholder="请选择所属分类">
          <el-option v-for="item in categoryOptions" :key="item.value" :label="item.label" :value="item.value" />
        </el-select>
      </el-form-item>
      <el-form-item label="方案类型" prop="categoryId">
        <el-select style="width: 100%" v-model="form.categoryId" placeholder="请选择方案类型">
          <el-option v-for="item in solutionTypeOptions" :key="item.id" :label="item.name" :value="item.id" />
        </el-select>
      </el-form-item>
      <el-form-item label="描述" prop="description">
        <el-input v-model="form.description" type="textarea" :rows="3" placeholder="请输入描述" />
      </el-form-item>
      <el-form-item label="文件" prop="file">
        <HUpload comTypes="pdf" :modelValue="form.file" @update:modelValue="(val) => form.file = val"
          :uploadTip="'支持上传pdf文件'" />
      </el-form-item>
      <el-form-item label="视频" prop="video">
        <HUpload comTypes="video" :modelValue="form.video"
          @update:modelValue="(val) => form.video = val" :uploadTip="'支持上传mp4视频文件'" />
      </el-form-item>
    </el-form>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="handleClose" :disabled="isSubmitting">取 消</el-button>
        <el-button type="primary" @click="submitForm" :loading="isSubmitting">确 定</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, reactive, watch, computed } from 'vue';
import { ElMessage } from 'element-plus';
import _ from 'lodash';
import HUpload from '@/components/HUpload/index.vue';
import API from '@/api/maintainance';
import { validateNoWhitespace } from '@/utils/validateRule';
import { useDictStore } from '@/stores/modules/dict';

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  data: {
    type: Object,
    default: () => ({})
  }
})

const emit = defineEmits(['update:visible', 'success']);

const dictStore = useDictStore();
const dialogVisible = ref(false);
const formRef = ref(null);
const isSubmitting = ref(false);
const categoryOptions = computed(() => dictStore.getDictByType('solution_type'));
const solutionTypeOptions = ref([]);
const form = reactive({
  id: '',
  name: '',
  solutionType: '',
  categoryId: '',
  description: '',
  file: '',
  video: ''
})
let solutionTypeOptionsTemp = []

const validateFileOrVideo = (rule, value, callback) => {
  if (!form.file && !form.video) {
    callback(new Error('请至少上传一个文件或视频'))
  } else {
    callback()
  }
}

const rules = reactive({
  name: [
    { required: true, message: '请输入方案名称', trigger: 'blur' },
    { validator: validateNoWhitespace, trigger: 'blur' },
    { max: 20, message: '方案名称不能超过20个字符', trigger: 'blur' }
  ],
  solutionType: [
    { required: true, message: '请选择所属分类', trigger: 'change' }
  ],
  categoryId: [
    { required: true, message: '请选择方案类型', trigger: 'change' }
  ],
  description: [
    { max: 500, message: '描述不能超过500个字符', trigger: 'blur' }
  ],
  file: [
    { validator: validateFileOrVideo, trigger: 'change' }
  ],
  video: [
    { validator: validateFileOrVideo, trigger: 'change' }
  ]
})

const getSolutionTypeOptions = async () => {
  try {
    const res = await API.getSolutionCategoryList()
    if (res?.success && res?.result) {
      solutionTypeOptionsTemp = res.result
    } else {
      ElMessage.error(res?.error || '获取方案类型失败')
    }
  } catch (error) {
    console.error('获取方案类型失败:', error)
    ElMessage.error('获取方案类型失败')
  }
}

let isTriggerManual = true
watch(() => props.visible, async (val) => {
  dialogVisible.value = val
  if (val) {
    await getSolutionTypeOptions()
  }
  if (val && props.data.id) {
    isTriggerManual = false
    Object.assign(form, props.data, {
      solutionType: solutionTypeOptionsTemp.find(item => item.id === props.data.categoryId)?.solutionType,
    })
  }
}, { immediate: true })

watch(() => form.solutionType, (newVal) => {
  if (isTriggerManual) {
    form.categoryId = null
  }
  solutionTypeOptions.value = solutionTypeOptionsTemp.filter(item => item.solutionType === newVal)
  isTriggerManual = true
})

const submitForm = async () => {
  if (isSubmitting.value) return;
  if (!formRef.value) return;

  const valid = await formRef.value.validate();
  if (valid) {
    isSubmitting.value = true;
    try {
      const params = _.cloneDeep(form);
      const res = form.id
        ? await API.editSolution(params)
        : await API.addSolution(params)
      if (res?.data?.success) {
        ElMessage.success(form.id ? '编辑成功' : '新增成功')
        emit('success', form)
        handleClose()
      } else {
        ElMessage.error(res?.data?.error || (form.id ? '编辑失败' : '新增失败'))
      }
    } catch (error) {
      console.error(form.id ? '编辑失败:' : '新增失败:', error)
      ElMessage.error(form.id ? '编辑失败' : '新增失败');
    } finally {
      isSubmitting.value = false;
    }
  }
};

const handleClose = () => {
  emit('update:visible', false)
  formRef.value?.resetFields()
  Object.assign(form, {
    id: '',
    name: '',
    solutionType: '',
    categoryId: '',
    description: '',
    file: '',
    video: ''
  })
}
</script>

<style scoped>
.upload-demo {
  width: 360px;
}

.el-upload__tip {
  line-height: 1.2;
  margin-top: 5px;
  color: #666;
}
</style>
