<template>
	<div style="height: 100%;">
		<el-tabs v-model="activeTab">
			<el-tab-pane label="待处理" name="TO_PROCESS" />
			<el-tab-pane label="已处理" name="HANDLED" />
		</el-tabs>
		<div class="wrap">
			<div class="cus-header">
				<el-form :model="formSearch" label-width="100px">
					<!-- 基础查询条件 -->
					<div class="form-item-grid" :class="[isExpanded ? 'is-expanded' : '']">
						<el-form-item label="电站类型">
							<el-select v-model="formSearch.stationType" placeholder="选择电站类型" style="width: 100%;"
								@change="getInspectionPlanList">
								<el-option v-for="item in stationTypeOptions" :key="item.value" :label="item.label"
									:value="item.value" />
							</el-select>
						</el-form-item>

						<el-form-item label="巡检类型">
							<el-select v-model="formSearch.inspectionType" placeholder="选择巡检类型" clearable style="width: 100%;"
								@change="getInspectionPlanList">
								<el-option v-for="item in inspectionTypeOptions" :key="item.value" :label="item.label"
									:value="item.value" />
							</el-select>
						</el-form-item>

						<el-form-item label="任务编号">
							<el-input v-model="formSearch.orderCode" placeholder="输入任务编号" clearable />
						</el-form-item>

						<el-form-item label="电站编码">
							<el-input v-model="formSearch.stationCode" placeholder="输入电站编码" clearable />
						</el-form-item>

						<el-form-item label="巡检计划">
							<el-select v-model="formSearch.planId" placeholder="选择巡检计划" clearable style="width: 100%;">
								<el-option v-for="item in planOptions" :key="item.value" :label="item.label" :value="item.value" />
							</el-select>
						</el-form-item>

						<el-form-item label="运维商">
							<el-input v-model="formSearch.opName" placeholder="输入运维商" clearable />
						</el-form-item>

						<el-form-item label="任务时间">
							<el-date-picker v-model="createTime" type="daterange" value-format="YYYY-MM-DD" start-placeholder="开始时间"
								end-placeholder="结束时间" @change="dateChange" clearable style="width: 100%;" />
						</el-form-item>
						<el-form-item label="是否超时">
							<el-select v-model="formSearch.overTime" placeholder="选择是否超时" clearable style="width: 100%;">
								<el-option key="yes" label="是" :value="true" />
								<el-option key="no" label="否" :value="false" />
							</el-select>
						</el-form-item>
						<!-- 查询按钮组 -->
						<div class="search-buttons">
							<el-button type="default" @click="onReset">重置</el-button>
							<el-button type="primary" @click="queryList">查询</el-button>
							<el-link type="primary" @click="toggleExpand" :underline="false" style="margin-left: 10px;">
								{{ isExpanded ? '收起' : '展开' }}
								<el-icon>
									<arrow-up v-if="isExpanded" />
									<arrow-down v-else />
								</el-icon>
							</el-link>
						</div>
					</div>
				</el-form>
			</div>
			<div class="cus-main" ref="mainRef">
				<div class="cus-list" v-loading="loading" ref="cusListRef">
					<el-table :data="listArr" class="cus-table" @selection-change="handleSelectionChange">
						<el-table-column fixed align="center" type="selection" width="55" />
						<el-table-column fixed align="center" type="index" label="序号" width="60" />
						<el-table-column fixed align="center" prop="orderCode" label="任务编号" width="200" />
						<el-table-column align="center" prop="stationCode" label="电站编码" width="160">
							<template #default="scope">
								{{ scope.row.stationCode || '-' }}
							</template>
						</el-table-column>
						<el-table-column align="center" prop="stationName" label="电站名称" width="100">
							<template #default="scope">
								{{ scope.row.stationName || '-' }}
							</template>
						</el-table-column>
						<el-table-column align="center" prop="stationType" label="电站类型" width="120">
							<template #default="scope">
								{{ getDictLabel('station_type', scope.row.stationType) }}
							</template>
						</el-table-column>
						<el-table-column align="center" prop="inspectionType" label="巡检类型" width="120">
							<template #default="scope">
								{{ getDictLabel('inspection_type', scope.row.inspectionType) }}
							</template>
						</el-table-column>
						<el-table-column align="center" prop="planName" label="巡检计划" width="120">
							<template #default="scope">
								{{ scope.row.planName || '-' }}
							</template>
						</el-table-column>
						<el-table-column align="center" prop="startDate" label="开始时间" width="120">
							<template #default="scope">
								{{ scope.row.startDate || '-' }}
							</template>
						</el-table-column>
						<el-table-column align="center" prop="endDate" label="结束时间" width="120">
							<template #default="scope">
								{{ scope.row.endDate || '-' }}
							</template>
						</el-table-column>
						<el-table-column align="center" prop="endDate" label="是否超时" width="120">
							<template #default="scope">
								{{ typeof scope.row.overTime === 'boolean' ? (scope.row.overTime ? '是' : '否') : '-' }}
							</template>
						</el-table-column>
						<el-table-column align="center" prop="specialFlag" label="资方">
							<template #default="scope">
								{{ pmSpecialFlag[scope.row.specialFlag] || '-' }}
							</template>
						</el-table-column>
						<el-table-column align="center" prop="opName" label="运维商" width="120">
							<template #default="scope">
								{{ scope.row.opName || '-' }}
							</template>
						</el-table-column>
						<el-table-column fixed="right" align="center" label="操作" width="80">
							<template #default="scope">
								<el-button link type="primary" @click="viewTask(scope.row)">查看</el-button>
							</template>
						</el-table-column>
					</el-table>
					<el-pagination class="cus-pages" v-if="total" background layout="sizes, prev, pager, next, ->, total"
						:page-sizes="[10, 20, 30]" :page-size="pagination.pageSize" :current-page="pagination.pageNum"
						:total="total" @size-change="changeSize" @current-change="changeCurrent" />
				</div>
			</div>
		</div>
	</div>
</template>

<script setup>
import { ref, reactive, onMounted, watch, computed } from 'vue';
import { useRouter } from 'vue-router';
import API from '@/api/maintainance';
import _ from 'lodash';
import _D from '@/edata/_osp_data';
import { ArrowDown, ArrowUp } from '@element-plus/icons-vue';
import { useTablePagination } from '@/composables/useTablePagination';
import { useDictStore } from '@/stores/modules/dict';

const isExpanded = ref(false);
const activeTab = ref('TO_PROCESS');
const createTime = ref('');
const router = useRouter();
const dictStore = useDictStore();
const pmSpecialFlag = _D.pmSpecialFlag;

const stationTypeOptions = computed(() => dictStore.getDictByType('station_type'));
const inspectionTypeOptions = computed(() => dictStore.getDictByType('inspection_type'));

const getDictLabel = (dictType, value) => {
	const dictMap = dictStore.getDictMapByType(dictType);
	return dictMap[value] || '-';
};

const formSearch = reactive({
	startDateEnd: null,
	startDateBegin: null,
	orderCode: '',
	stationCode: '',
	inspectionType: '',
	stationName: '',
	planId: '',
	stationType: 'COMMON',
	opName: '',
	overTime: null
});

const {
	loading,
	listArr,
	total,
	pagination,
	getList,
	queryList,
	changeSize,
	changeCurrent,
} = useTablePagination(
	params => API.getInspectionWorkOrderPage({ ...params, orderStatus: activeTab.value }),
	() => formSearch,
	{ manual: true }
);

const dateChange = (e) => {
	if (e) {
		formSearch.startDateBegin = e[0];
		formSearch.startDateEnd = e[1];
	} else {
		formSearch.startDateBegin = null;
		formSearch.startDateEnd = null;
	}
};

const onReset = _.throttle(
	() => {
		createTime.value = '';
		Object.assign(formSearch, {
			startDateEnd: null,
			startDateBegin: null,
			orderCode: '',
			stationCode: '',
			inspectionType: '',
			stationName: '',
			planId: '',
			stationType: 'COMMON',
			opName: ''
		});
		planOptions.value = [];
		queryList();
		getInspectionPlanList();
	},
	3000,
	{
		trailing: false
	}
);

const toggleExpand = () => {
	isExpanded.value = !isExpanded.value;
};

const viewTask = (row) => {
	router.push({
		path: '/maintainance/inspect/task/detail',
		query: {
			orderCode: row.orderCode,
			action: 'view'
		}
	});
};

watch(activeTab, () => {
	queryList();
});

const planOptions = ref([]);

const getInspectionPlanList = () => {
	const params = _.cloneDeep({
		stationType: formSearch.stationType,
		inspectionType: formSearch.inspectionType
	});
	API.getInspectionPlanList(params).then(res => {
		if (res.success) {
			formSearch.planId = '';
			planOptions.value = res.result.map(item => ({
				value: item.id,
				label: item.planName
			}));
		}
	});
};

onMounted(() => {
	getList();
	getInspectionPlanList();
	dictStore.fetchDict([
		'inspection_type',
		'station_type',
	]);
});
</script>

<style lang="less" scoped>
@import '@/assets/style/_cus_header.less';
@import '@/assets/style/_cus_list.less';

:deep(.el-tabs__nav-scroll) {
	background-color: var(--el-bg-color);
	padding-left: 12px;
}

.wrap {
	height: calc(100% - 54px);
}

.cus-header {
	margin-bottom: 0px;

	.form-item-grid {
		display: grid;
		grid-template-columns: repeat(3, 1fr);
		gap: 12px;
		width: 100%;

		/* 默认状态（收起）- 只显示前两个元素和按钮组 */
		.el-form-item:nth-child(n+3):not(:last-child) {
			display: none;
		}

		/* 展开状态 - 显示所有元素 */
		&.is-expanded {
			.el-form-item:nth-child(n+3):not(:last-child) {
				display: flex;
			}
		}
	}

	.search-buttons {
		display: flex;
		align-items: center;
		justify-content: flex-end;
		grid-column: -2 / -1;
	}

	.el-form-item {
		width: 100%;
		margin-bottom: 0px;

		.el-input,
		.el-select,
		.el-date-editor {
			width: 100%;
		}
	}
}

.cus-main {
	display: flex;
	flex-direction: column;
	flex: 1;
	overflow: hidden;

	>.el-button {
		margin-bottom: 10px;
	}

	.cus-list {
		display: flex;
		flex-direction: column;
		flex: 1;
		overflow: hidden;
		position: relative;

		.cus-table {
			flex: 1;
			overflow: auto;
			height: 100%;
		}

		.cus-pages {
			margin-top: 10px;
		}
	}
}
</style>
