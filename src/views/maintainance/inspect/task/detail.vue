<template>
  <div class="inpsect-task-page">
    <el-page-header class="cus-back" icon="ArrowLeft" @back="$router.go(-1)" />
    <!-- 故障工单信息 -->
    <el-card class="section-card">
      <template #header>
        <div class="card-header">巡检任务</div>
      </template>
      <el-descriptions :column="3" border>
        <el-descriptions-item label="任务编号">{{ formData.orderCode || '-' }}</el-descriptions-item>
        <el-descriptions-item label="任务状态">{{ getDictLabel('work_order_status', formData.orderStatus) || '-'
        }}</el-descriptions-item>
        <el-descriptions-item label="电站类型">{{ getDictLabel('station_type',
          formData.stationType) }}</el-descriptions-item>
        <el-descriptions-item label="巡检类型">{{ getDictLabel('inspection_type',
          formData.inspectionType) }}</el-descriptions-item>
        <el-descriptions-item label="巡检计划">{{ formData.planName || '-' }}</el-descriptions-item>
        <el-descriptions-item label="开始时间">{{ formData.startDate || '-' }}</el-descriptions-item>
        <el-descriptions-item label="结束时间">{{ formData.endDate || '-' }}</el-descriptions-item>
        <el-descriptions-item label="是否超时">{{ typeof formData.overTime === 'boolean' ? (formData.overTime ? '是' : '否') :
          '-'
          }}</el-descriptions-item>
      </el-descriptions>
    </el-card>

    <!-- 电站信息 -->
    <el-card class="section-card">
      <template #header>
        <div class="card-header">
          <span>电站信息</span>
        </div>
      </template>
      <el-descriptions :column="2" border>
        <el-descriptions-item label="电站编码">{{ formData.stationCode || '-' }}</el-descriptions-item>
        <el-descriptions-item label="逆变器SN码">
          <el-button v-if="formData.inverterSn" link>
            {{ formData.inverterSn }}
          </el-button>
          <span>-</span>
        </el-descriptions-item>
        <el-descriptions-item label="业主姓名">{{ formData.stationName || '-' }}</el-descriptions-item>
        <el-descriptions-item label="手机号">{{ formData.stationPhone || '-' }}</el-descriptions-item>
        <el-descriptions-item label="电站模式">{{ formData.stationMode || '-' }}</el-descriptions-item>
        <el-descriptions-item label="关联资方">{{ pmSpecialFlag[formData.specialFlag] || '-' }}</el-descriptions-item>
        <el-descriptions-item label="所属分中心">{{ formData.subCenterName || '-' }}</el-descriptions-item>
        <el-descriptions-item label="服务商类别">{{ identityType[formData.opType] || '-' }}</el-descriptions-item>
        <el-descriptions-item label="运维商名称">{{ formData.opName || '-' }}</el-descriptions-item>
        <el-descriptions-item label="运维商业务类型">{{ businessType[formData.businessType] || '-' }}</el-descriptions-item>
        <el-descriptions-item label="是否质保期内">{{ isWarranty[formData.isWarranty] || '-' }}</el-descriptions-item>
        <el-descriptions-item label="区域">{{ (formData.provinceName || '') + (formData.cityName || '') +
          (formData.regionName
            || '') || '-' }}</el-descriptions-item>
        <el-descriptions-item label="详细地址">{{ formData.address || '-' }}</el-descriptions-item>
      </el-descriptions>
    </el-card>

    <el-card class="section-card">
      <template #header>
        <div class="card-header">
          <span>巡检项</span>
        </div>
      </template>
      <el-form ref="formRef" :model="formData" label-position="top" :rules="rules" label-width="120px"
        class="form-container">
        <div v-for="(item, idx) in formData.handleItems" :key="'cfg' + idx">
          <el-form-item v-if="item.resultType === 'text'" :label="item.checkItem"
            :prop="`handleItems[${idx}].resultContent`"
            :rules="[{ required: true, message: `请输入${item.checkItem}`, trigger: 'blur' }]">
            <el-input v-model="item.resultContent" type="textarea" rows="3" :placeholder="`请输入${item.checkItem}`"
              :disabled="!editable" />
          </el-form-item>

          <el-form-item v-if="item.resultType === 'select'" :label="item.checkItem"
            :prop="`handleItems[${idx}].resultContent`"
            :rules="[{ required: true, message: `请选择${item.checkItem}`, trigger: 'change' }]">
            <el-select v-model="item.resultContent" :placeholder="`请选择${item.checkItem}`" :disabled="!editable">
              <el-option key="良好" label="良好" value="良好">
              </el-option>
              <el-option key="损坏" label="损坏" value="损坏">
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item v-if="item.resultType === 'image'" :label="item.checkItem"
            :prop="`handleItems[${idx}].resultContent`" :rules="[{
              required: true, message: `请上传${item.checkItem}照片`, trigger: 'change', validator: (rule, value, callback) => {
                if (!value || (Array.isArray(value) && value.length === 0)) {
                  callback(new Error(`请上传${item.checkItem}照片`));
                } else {
                  callback();
                }
              }
            }]">
            <el-row :gutter="20" style="width: 100%;">
              <el-col :span="6">
                <el-form-item label="示例照片">
                  <el-image style="width: 150px; height: 150px; background-color: #eee;" :src="item.exampleImage"
                    fit="cover">
                    <template #error>
                      <div class="image-slot">示例图</div>
                    </template>
                  </el-image>
                </el-form-item>
              </el-col>
              <el-col :span="18" v-if="editable || (!editable && item.resultContent && item.resultContent.length > 0)">
                <el-form-item label="上传照片">
                  <HMultiUpload v-model="item.resultContent" :limit="3" list-type="picture-card"
                    :disabled="!editable" />
                </el-form-item>
              </el-col>
            </el-row>
          </el-form-item>
        </div>
        <el-form-item label="备注" prop="remark">
          <el-input v-model="formData.remark" type="textarea" rows="3" placeholder="请输入备注" :disabled="!editable" />
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 底部操作按钮 -->
    <!-- <div class="action-buttons">
      <el-button v-if="['TO_PROCESS'].includes(formData.orderStatus)" type="success"
        @click="handleSubmit">提交</el-button>
    </div> -->
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { ElMessage, ElImage } from 'element-plus'
import { useRoute, useRouter } from 'vue-router'
import API from '@/api/maintainance'
import HMultiUpload from '@/components/HMultiUpload/index.vue'
import { useDictStore } from '@/stores/modules/dict';
import _D from '@/edata/_osp_data';

const route = useRoute()
const router = useRouter()
const dictStore = useDictStore();

const action = route.query.action

const pmSpecialFlag = _D.pmSpecialFlag;
const businessType = _D.businessType;
const isWarranty = _D.isWarranty;
const identityType = _D.identityType;

const editable = computed(() => {
  return action !== 'view';
})

const getDictLabel = (dictType, value) => {
  const dictMap = dictStore.getDictMapByType(dictType);
  return dictMap[value] || '-';
};

// 表单数据 - 包含截图中的所有字段（使用假设的字段名）
const formData = ref({
  address: '',
  businessType: '',
  cityId: '',
  cityName: '',
  configItems: [],
  createdAt: '',
  endDate: '',
  handleItems: [],
  handleTime: '',
  handler: '',
  handlerId: '',
  id: '',
  inspectionType: '',
  opMemberId: '',
  opName: '',
  orderCode: '',
  orderName: '',
  orderStatus: '',
  orderType: '',
  overTime: null,
  planId: '',
  planName: '',
  provinceId: '',
  provinceName: '',
  regionId: '',
  regionName: '',
  remark: '',
  specialFlag: '',
  startDate: '',
  stationCode: '',
  stationId: '',
  stationMode: '',
  stationName: '',
  stationPhone: '',
  stationType: '',
  streetId: '',
  streetName: '',
  subCenterCode: '',
  subCenterName: ''
})

// 获取详情数据
const getDetail = async () => {
  const orderCode = route.query.orderCode
  if (!orderCode) {
    ElMessage.error('缺少工单标识')
    router.back()
    return
  }
  formData.value.orderCode = orderCode

  try {
    const res = await API.getInspectionWorkOrder({ orderCode })

    if (res.success) {
      Object.assign(formData.value, res.result)

      if (formData.value.createdAt) {
        formData.value.createdAt = formData.value.createdAt.replace('T', ' ')
      }
    } else {
      ElMessage.error(res.error || '获取详情失败')
    }
  } catch (error) {
    console.error('获取详情失败:', error)
    ElMessage.error('获取详情数据时发生错误')
  }
}

const formRef = ref(null);

// 提交处理
// const handleSubmit = async () => {
//   if (!formRef.value) return;

//   formRef.value.validate(async (valid) => {
//     if (!valid) {
//       ElMessage.error('请完成所有必填项');
//       return;
//     }

//     ElMessageBox.confirm('确认提交工单处理结果吗?', '提示', {
//       confirmButtonText: '确定',
//       cancelButtonText: '取消',
//       type: 'warning',
//     }).then(async () => {
//       const params = {
//         orderCode: formData.value.orderCode,
//         remark: formData.value.remark,
//         handleCheckItems: formData.value.handleCheckItems?.map(item => {
//           if (item.resultType === 'image' && Array.isArray(item.resultContent)) {
//             return {
//               ...item,
//               resultContent: item.resultContent.join(',')
//             }
//           }
//           return item;
//         }) || []
//       }

//       try {
//         const res = await API.handleWorkOrder(params)

//         if (res.data.success) {
//           ElMessage.success('提交成功')
//           router.back()
//         } else {
//           ElMessage.error(res.data.error || '提交失败')
//         }
//       } catch (error) {
//         console.error('提交失败:', error)
//         ElMessage.error('提交处理结果时发生错误')
//       }
//     }).catch(() => {
//       // 取消提交
//     });
//   });
// }

// 组件挂载后获取数据
onMounted(() => {
  getDetail()
  dictStore.fetchDict([
    'fault_level',
    'work_order_type',
    'work_order_status',
    'dispatch_mode',
    'dispatch_review_permission',
    'review_mode',
    'work_order_source',
    'close_order_reason',
    'audit_reject_reason'
  ]);
})

</script>

<style lang="less" scoped>
@import '@/assets/style/_cus_header.less';
@import '@/assets/style/_cus_view.less';

.inpsect-task-page {
  padding: 20px;
  background-color: #f8f9fa;
  height: 100%;
  box-sizing: border-box;
  overflow: auto;

  .section-card {
    margin-bottom: 20px;
    border-radius: 4px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);

    .card-header {
      display: flex;
      align-items: center;
      font-size: 16px;
      font-weight: 600;
      color: #303133;
    }

    :deep(.el-card__header) {
      background-color: #ffffff;
      padding: 12px 20px;
      border-bottom: 1px solid #e9ecef;
    }

    :deep(.el-card__body) {
      padding: 20px;
    }

    :deep(.el-descriptions__label) {
      font-weight: normal;
      color: #6c757d;
      background-color: #f8f9fa;
    }

    :deep(.el-descriptions__content) {
      color: #212529;
    }

    :deep(.el-descriptions__cell) {
      padding: 8px 12px;
    }
  }

  .info-label {
    color: #6c757d;
    margin-bottom: 8px;
    font-size: 14px;
  }

  .image-slot {
    display: flex;
    justify-content: center;
    align-items: center;
    width: 100%;
    height: 100%;
    background: #f5f7fa;
    color: #c0c4cc;
    font-size: 14px;
  }

  // 工单处理表单样式
  .el-form {
    :deep(.el-form-item__label) {
      font-weight: 600;
      color: #495057;
      padding-bottom: 4px;
    }

    // 上传组件样式
    :deep(.el-upload--picture-card) {
      width: 150px;
      height: 150px;
    }

    :deep(.el-upload-list--picture-card .el-upload-list__item) {
      width: 150px;
      height: 150px;
    }
  }


  .action-buttons {
    margin-top: 20px;
    padding: 15px 20px;
    background-color: #ffffff;
    border-top: 1px solid #e9ecef;
    text-align: right;
    position: sticky;
    bottom: 0;
    z-index: 10;
  }
}

.handle-check-items {
  margin-top: 10px;
  padding: 10px;
  background-color: #f8f8f8;
  border-radius: 4px;

  .check-items-title {
    font-weight: bold;
    margin-bottom: 8px;
    color: #409EFF;
  }
}
</style>
