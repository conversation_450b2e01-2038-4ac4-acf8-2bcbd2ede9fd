<template>
	<div style="height: 100%;">
		<el-tabs v-model="activeTab">
			<el-tab-pane label="户用" name="COMMON"></el-tab-pane>
			<el-tab-pane label="工商业" name="CM"></el-tab-pane>
			<el-tab-pane label="公共租赁" name="PUB_BUILD"></el-tab-pane>
		</el-tabs>
		<div class="cus-container">
			<div class="cus-tree" ref="treeContainer">
				<div class="button-group">
					<el-button type="primary" @click="addPlan">添加</el-button>
					<el-button type="primary" @click="editPlan">编辑</el-button>
					<el-button type="danger" @click="deletePlan">删除</el-button>
				</div>
				<div>
					<el-input v-model="filterText" placeholder="输入关键字过滤" clearable>
						<template #append>
							<el-button icon="search" />
						</template>
					</el-input>
				</div>
				<el-tree-v2 ref="myTree" node-key="id" :expand-on-click-node="false"
					:default-expanded-keys="['ANNUAL', 'TEMPORARY']" highlight-current check-on-click-node :data="planTree"
					:current-node-key="node" :filter-method="filterNode" @node-click="nodeChange" @node-expand="handleNodeExpand"
					@node-collapse="handleNodeCollapse" :height="treeHeight" :props="{ children: 'children', label: 'label' }" />
			</div>
			<div style="display: flex;flex-direction: column;gap: 12px;flex: 1;width: 0;">
				<div class="statistic-container">
					<div class="statistic-item">
						<span>巡检电站数量</span>
						<span>{{ statisticTotal?.stationCount || '0' }}</span>
					</div>
					<div class="statistic-item">
						<span>已巡检</span>
						<span>{{ statisticTotal?.inspectedCount || '0' }}</span>
					</div>
					<div class="statistic-item">
						<span>巡检进度</span>
						<span>{{ statisticTotal?.progress ? statisticTotal?.progress + '%' : '0%' }}</span>
					</div>
				</div>
				<div class="wrap">
					<div class="cus-header">
						<el-form :model="formSearch" label-width="72px">
							<div class="form-item-grid" :class="[isExpanded ? 'is-expanded' : '']">
								<el-form-item label="运维商">
									<el-input v-model="formSearch.opName" placeholder="输入运维商" clearable />
								</el-form-item>
								<el-form-item label="资方所属">
									<el-select v-model="formSearch.specialFlag" placeholder="选择资方所属" clearable style="width: 100%;">
										<el-option v-for="item in capitalBelongOptions" :key="item.value" :label="item.label"
											:value="item.value" />
									</el-select>
								</el-form-item>
								<el-form-item label="省">
									<el-select v-model="formSearch.provinceId" @change="provinceChange" placeholder="省" clearable>
										<el-option v-for="(item, index) in provinceOptions" :value="item.id" :label="item.name"
											:key="index" />
									</el-select>
								</el-form-item>
								<el-form-item label="市">
									<el-select v-model="formSearch.cityId" @change="cityChange" placeholder="市" clearable>
										<el-option v-for="(item, index) in cityOptions" :value="item.id" :label="item.name" :key="index" />
									</el-select>
								</el-form-item>
								<el-form-item label="区">
									<el-select v-model="formSearch.regionId" placeholder="区" clearable>
										<el-option v-for="(item, index) in regionsOptions" :value="item.id" :label="item.name"
											:key="index" />
									</el-select>
								</el-form-item>
								<el-form-item label="巡检进度">
									<div style="flex: 1;width: 0;">
										<el-input-number v-model="formSearch.progressMin" :min="0" :max="formSearch.progressMax || 100"
											placeholder="最小进度" controls-position="right" style="width: 100%"/>
									</div>
									<div style="margin: 0px 4px;">~</div>
									<div style="flex: 1;width: 0;">
										<el-input-number v-model="formSearch.progressMax" :min="formSearch.progressMin || 0" :max="100"
											placeholder="最大进度" controls-position="right" style="width: 100%"/>
									</div>
								</el-form-item>
								
								<div class="search-buttons">
									<el-button type="default" @click="onReset">重置</el-button>
									<el-button type="primary" @click="queryList">查询</el-button>
									<el-link type="primary" @click="toggleExpand" :underline="false" style="margin-left: 10px;">
										{{ isExpanded ? '收起' : '展开' }}
										<el-icon>
											<arrow-up v-if="isExpanded" />
											<arrow-down v-else />
										</el-icon>
									</el-link>
								</div>
							</div>
						</el-form>
					</div>
					<div class="cus-main" ref="mainRef">
						<div class="cus-list" v-loading="loading" ref="cusListRef">
							<el-table :data="listArr" class="cus-table" :empty-text="emptyText">
								<el-table-column fixed align="center" type="index" label="序号" width="60" />
								<el-table-column align="center" prop="opName" label="运维商名称">
									<template #default="scope">
										{{ scope.row.opName || '-' }}
									</template>
								</el-table-column>
								<el-table-column align="center" prop="specialFlag" label="资方">
									<template #default="scope">
										{{ pmSpecialFlag[scope.row.specialFlag] || '-' }}
									</template>
								</el-table-column>
								<!-- <el-table-column align="center" prop="provinceName" label="省" width="100">
									<template #default="scope">
										{{ scope.row.provinceName || '-' }}
									</template>
								</el-table-column>
								<el-table-column align="center" prop="cityName" label="市" width="100">
									<template #default="scope">
										{{ scope.row.cityName || '-' }}
									</template>
								</el-table-column>
								<el-table-column align="center" prop="regionName" label="区" width="100">
									<template #default="scope">
										{{ scope.row.regionName || '-' }}
									</template>
								</el-table-column> -->
								<el-table-column align="center" prop="stationCount" label="电站数量" width="120">
									<template #default="scope">
										{{ scope.row.stationCount === null || scope.row.stationCount === undefined ? '-' :
											scope.row.stationCount }}
									</template>
								</el-table-column>
								<el-table-column align="center" prop="inspectedCount" label="已巡检数量" width="120">
									<template #default="scope">
										{{ scope.row.inspectedCount === null || scope.row.inspectedCount === undefined ? '-' :
											scope.row.inspectedCount }}
									</template>
								</el-table-column>
								<el-table-column align="center" prop="progress" label="巡检进度" width="120">
									<template #default="scope">
										{{ scope.row.progress === null || scope.row.progress === undefined ? '-' : scope.row.progress + '%'
										}}
									</template>
								</el-table-column>
							</el-table>
							<el-pagination class="cus-pages" v-if="total" background layout="sizes, prev, pager, next, ->, total"
								:page-sizes="[10, 20, 30]" :page-size="pagination.pageSize" :current-page="pagination.pageNum"
								:total="total" @size-change="changeSize" @current-change="changeCurrent" />
						</div>
					</div>
					<OperatePlan v-model:visible="operatePlanVisible" :data="operatePlanData" @success="handlePlanSuccess" />
				</div>
			</div>
		</div>
	</div>
</template>

<script setup>
import { ref, reactive, onMounted, watch, nextTick, onUnmounted } from 'vue';
import API from '@/api/maintainance';
import _API from '@/api/epc';
import _ from 'lodash';
import _D from '@/edata/_osp_data';
import { ElMessageBox, ElMessage } from 'element-plus';
import { useTablePagination } from '@/composables/useTablePagination';
import { useDictStore } from '@/stores/modules/dict';
import OperatePlan from './components/operatePlan.vue';

const dictStore = useDictStore();
const capitalBelongOptions = _D.property;
const pmSpecialFlag = _D.pmSpecialFlag;

const isExpanded = ref(false);
const activeTab = ref('COMMON');
const createTime = ref('');

const planTree = ref([]);
const node = ref(null);
const myTree = ref(null);
const treeContainer = ref(null);
const treeHeight = ref(200);
const expandedKeys = ref([]);
const filterText = ref('');

const operatePlanVisible = ref(false);
const operatePlanData = ref({});
const statisticTotal = ref({
	inspectedCount: 0,
	progress: 0,
	stationCount: 0
});

const formSearch = reactive({
	orderCode: '',
	stationCode: '',
	opName: '',
	specialFlag: '',
	inspectionType: null,
	provinceId: '',
	cityId: '',
	regionId: '',
	progressMin: null,
	progressMax: null,
});

const provinceOptions = ref([]);
const cityOptions = ref([]);
const regionsOptions = ref([]);
const addressList = ref({});
const emptyText = ref("暂无数据")

const {
	loading,
	listArr,
	total,
	pagination,
	getList,
	queryList,
	changeSize,
	changeCurrent,
} = useTablePagination(
	params => {
		const currentNode = myTree.value?.getCurrentNode()
		getInspectionWorkOrderStatisticsTotal();
		if (!currentNode || !currentNode.parentId) {
			emptyText.value = '请选择巡检计划';
			return Promise.resolve({ result: { content: [], totalElements: 0 }, success: true });
		}
		emptyText.value = '暂无数据';
		return API.getInspectionWorkOrderStatistics({ ...params, stationType: activeTab.value, planId: currentNode.id })
	},
	() => formSearch,
	{ manual: true }
);

const onReset = _.throttle(
	() => {
		createTime.value = '';
		Object.assign(formSearch, {
			orderCode: '',
			stationCode: '',
			opName: '',
			specialFlag: '',
			provinceId: '',
			cityId: '',
			regionId: '',
			progressMin: null,
			progressMax: null,
		});
		cityOptions.value = [];
		regionsOptions.value = [];
		queryList();
	},
	3000,
	{
		trailing: false
	}
);

const toggleExpand = () => {
	isExpanded.value = !isExpanded.value;
};

watch(filterText, (val) => {
	myTree.value?.filter(val);
});

const filterNode = (value, data) => {
	if (!value) return true;
	return data.label.toLowerCase().includes(value.toLowerCase());
};

const calculateTreeHeight = () => {
	if (!treeContainer.value) return;
	const containerHeight = treeContainer.value.clientHeight;
	const otherElementsHeight = 100;
	const calculatedHeight = containerHeight - otherElementsHeight - 20;
	treeHeight.value = Math.max(calculatedHeight, 100);
};

const addPlan = () => {
	operatePlanData.value = {
		inspectionType: "TEMPORARY",
		stationType: activeTab.value
	};
	operatePlanVisible.value = true;
};

const editPlan = () => {
	const currentNode = myTree.value?.getCurrentNode();
	if (!currentNode || currentNode.children !== undefined || currentNode.parentId !== 'TEMPORARY') {
		ElMessage.warning('请先选择临时巡检计划的（二级节点）进行编辑。');
		return;
	}
	API.getInspectionPlan({ id: currentNode.id }).then(res => {
		if (res.success && res.result) {
			operatePlanData.value = { ...res.result };
			operatePlanVisible.value = true;
		} else {
			ElMessage.error(res.error || res.message || '获取计划详情失败');
		}
	}).catch(err => {
		ElMessage.error('获取计划详情接口请求失败');
	});
};

const deletePlan = () => {
	const currentNode = myTree.value?.getCurrentNode();
	if (!currentNode || currentNode.children !== undefined) {
		ElMessage.warning('请先选择一个具体的巡检计划（二级节点）进行删除。');
		return;
	}
	ElMessageBox.confirm(`确认要删除巡检计划 "${currentNode.label}" 吗？`, '提示', {
		confirmButtonText: '确定',
		cancelButtonText: '取消',
		type: 'warning',
	}).then(async () => {
		try {
			const res = await API.deleteInspectionPlan({ id: currentNode.id });
			if (res && res.data && res.data.success) {
				ElMessage.success('删除成功');
				getInspectionTypeDict();
			} else {
				ElMessage.error(res?.data?.error || res?.data?.message || '删除失败');
			}
		} catch (error) {
			console.error('删除巡检计划失败:', error);
			ElMessage.error(error?.data?.error || error?.data?.message || '删除操作失败');
		}
	}).catch(() => { });
};

const handlePlanSuccess = () => {
	getInspectionTypeDict();
}

const handleNodeExpand = (data) => {
	if (!expandedKeys.value.includes(data.id)) {
		expandedKeys.value.push(data.id);
	}
};

const handleNodeCollapse = (data) => {
	expandedKeys.value = expandedKeys.value.filter(key => key !== data.id);
};

const nodeChange = (data) => {
	if (node.value === data.id) {
		node.value = null;
		formSearch.inspectionType = null;
		myTree.value?.setCurrentKey(null);
	} else {
		node.value = data.id;
		if (data.children !== undefined) {
			formSearch.inspectionType = data.id;
		} else {
			formSearch.inspectionType = data.parentId;
		}
	}
	setTimeout(() => {
		queryList();
	}, 0);
};

const getInspectionTypeDict = async () => {
	if (myTree.value?.getExpandedKeys) {
		expandedKeys.value = myTree.value.getExpandedKeys();
	}
	await dictStore.fetchDict('inspection_type');
	const inspectionTypeDictData = dictStore.getDictByType('inspection_type');

	if (inspectionTypeDictData && inspectionTypeDictData.length > 0) {
		const planPromises = inspectionTypeDictData.map(async (type) => {
			try {
				const planRes = await API.getInspectionPlanList({
					inspectionType: type.value,
					stationType: activeTab.value
				});
				let children = [];
				if (planRes.success && planRes.result && Array.isArray(planRes.result)) {
					children = planRes.result.map(plan => ({
						label: plan.planName,
						id: plan.id,
						parentId: type.value
					}));
				}
				return {
					label: type.label,
					id: type.value,
					children: children,
				};
			} catch (error) {
				return {
					label: type.label,
					id: type.value,
					children: [],
				};
			}
		});
		planTree.value = await Promise.all(planPromises);
	} else {
		planTree.value = [];
	}

	nextTick(() => {
		if (myTree.value && expandedKeys.value.length > 0) {
			myTree.value.setExpandedKeys(expandedKeys.value);
		}
		calculateTreeHeight();
	});
};

const getInspectionWorkOrderStatisticsTotal = async () => {
	const currentNode = myTree.value?.getCurrentNode();
	if (!currentNode || !currentNode.parentId) {
		statisticTotal.value = {
			inspectedCount: 0,
			progress: 0,
			stationCount: 0
		};
		return;
	}
	const res = await API.getInspectionWorkOrderStatisticsTotal({ ...formSearch, planId: currentNode.id });
	statisticTotal.value = res.result;
};

const addResizeListener = () => {
	window.addEventListener('resize', _.debounce(calculateTreeHeight, 200));
};

const removeResizeListener = () => {
	window.removeEventListener('resize', calculateTreeHeight);
};

watch(activeTab, (newVal) => {
	formSearch.inspectionType = null;
	if (myTree.value) {
		myTree.value.setCurrentKey(null);
	}
	node.value = null;
	getInspectionTypeDict();
	queryList();
});

const getAreaList = () => {
	_API.getRegionList().then(res => {
		if (res.success && res.result) {
			for (let i in res.result.province_list) {
				provinceOptions.value.push({ id: i, name: res.result.province_list[i] });
			}
			addressList.value = res.result;
		}
	});
};

const provinceChange = (item) => {
	cityOptions.value = [];
	formSearch.cityId = '';
	formSearch.regionId = '';
	regionsOptions.value = [];
	if (!item) return;
	const str = item.substring(0, 2);
	for (let i in addressList.value.city_list) {
		if (str === String(i).substring(0, 2)) {
			cityOptions.value.push({ id: i, name: addressList.value.city_list[i] });
		}
	}
};

const cityChange = (item) => {
	regionsOptions.value = [];
	formSearch.regionId = '';
	if (!item) return;
	const str = item.substring(0, 4);
	for (let i in addressList.value.county_list) {
		if (str === String(i).substring(0, 4)) {
			regionsOptions.value.push({ id: i, name: addressList.value.county_list[i] });
		}
	}
};

onMounted(() => {
	getList();
	dictStore.fetchDict([
		'station_type',
	]);
	getInspectionTypeDict();
	getAreaList();

	nextTick(() => {
		calculateTreeHeight();
		addResizeListener();
	});
});

onUnmounted(() => {
	removeResizeListener();
});
</script>

<style lang="less" scoped>
@import '@/assets/style/_cus_header.less';
@import '@/assets/style/_cus_list.less';

:deep(.el-tabs__nav-scroll) {
	background-color: var(--el-bg-color);
	padding-left: 12px;
}

.cus-container {
	display: flex;
	gap: 20px;
	height: calc(100% - 54px);
	width: 100%;

	.button-group {
		display: flex;
		justify-content: space-between;

		>* {
			flex: 1
		}
	}
}

.cus-tree {
	width: 20%;
	min-width: 230px;
	border-radius: 6px;
	box-sizing: border-box;
	padding: 12px;
	background-color: var(--el-bg-color);
	height: 100%;
	overflow: hidden;

	display: flex;
	flex-direction: column;
	gap: 10px;

	.el-tree,
	.el-tree-v2 {
		flex: 1;
		max-height: 100%;
		overflow: auto;
	}
}

.wrap {
	height: calc(100% - 150px);
	width: 100%;
}

.cus-header {
	margin-bottom: 0px;

	.form-item-grid {
		display: grid;
		grid-template-columns: repeat(3, 1fr);
		gap: 12px;
		width: 100%;

		.el-form-item:nth-child(n+3):not(:last-child) {
			display: none;
		}

		&.is-expanded {
			.el-form-item:nth-child(n+3):not(:last-child) {
				display: flex;
			}
		}
	}

	.search-buttons {
		display: flex;
		align-items: center;
		justify-content: flex-end;
		grid-column: -2 / -1;
	}

	.el-form-item {
		width: 100%;
		margin-bottom: 0px;

		.el-input,
		.el-select,
		.el-date-editor {
			width: 100%;
		}
	}
}

.statistic-container {
	display: flex;
	gap: 24px;
}

.statistic-item {
	// background: var(--el-color-primary);
	padding: 15px 20px; // Target padding
	// border-radius: 10px; // Target border-radius
	color: #91929E;
	text-align: center;
	min-width: 160px; // Target min-width
	box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
	display: flex;
	flex-direction: column;
	justify-content: center;
	align-items: center;
	background-color: var(--el-bg-color);
	flex: 1;

	span {
		display: block;
		line-height: 1.4;

		&:first-child {
			// Label
			font-size: 13px; // Target label font size
			opacity: 0.9; // Target opacity
			font-weight: 700;
			margin-bottom: 8px; // Target margin
		}

		&:last-child {
			// Value
			font-size: 28px; // Target value font size
			font-weight: 700;
			color: rgb(143, 137, 137);
		}
	}
}

.cus-main {
	display: flex;
	flex-direction: column;
	flex: 1;
	overflow: hidden;

	>.el-button {
		margin-bottom: 10px;
	}

	.cus-list {
		display: flex;
		flex-direction: column;
		flex: 1;
		overflow: hidden;
		position: relative;

		.cus-table {
			flex: 1;
			overflow: auto;
			height: 100%;
		}

		.cus-pages {
			margin-top: 10px;
		}
	}
}
</style>
