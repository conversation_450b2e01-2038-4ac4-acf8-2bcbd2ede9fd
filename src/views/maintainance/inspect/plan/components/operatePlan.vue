<template>
  <el-dialog :title="form.id ? '编辑巡检计划' : '新增巡检计划'" v-model="dialogVisible" width="650px" :close-on-click-modal="false"
    @close="handleClose" append-to-body>
    <el-form ref="formRef" :model="form" :rules="rules" label-width="120px">
      <el-form-item label="计划名称" prop="planName">
        <el-input v-model="form.planName" placeholder="请输入计划名称" />
      </el-form-item>
      <el-form-item label="巡检类型" prop="inspectionType">
        <el-select v-model="form.inspectionType" placeholder="请选择巡检类型" style="width:100%" :disabled="true">
          <el-option v-for="item in inspectionTypeOptions" :key="item.value" :label="item.label" :value="item.value" />
        </el-select>
      </el-form-item>
      <el-form-item label="电站类型" prop="stationType">
        <el-select v-model="form.stationType" placeholder="请选择电站类型" style="width:100%" @change="handleStationChange">
          <el-option v-for="item in stationTypeOptions" :key="item.value" :label="item.label" :value="item.value" />
        </el-select>
      </el-form-item>
      <el-form-item label="巡检配置" prop="configId">
        <el-select v-model="form.configId" placeholder="请选择巡检配置" style="width:100%">
          <el-option v-for="item in configOptions" :key="item.value" :label="item.label" :value="item.value" />
        </el-select>
      </el-form-item>
      <el-form-item label="计划开始日期" prop="startDate">
        <el-date-picker v-model="form.startDate" type="date" placeholder="选择开始日期" style="width:100%"
          value-format="YYYY-MM-DD" :disabled-date="disabledDate" />
      </el-form-item>
      <el-form-item label="计划结束日期" prop="endDate">
        <el-date-picker v-model="form.endDate" type="date" placeholder="选择结束日期" style="width:100%"
          value-format="YYYY-MM-DD" :disabled-date="disabledDate" />
      </el-form-item>
      <el-form-item label="关联分中心" prop="subCenterCodeList">
        <el-select v-model="form.subCenterCodeList" multiple placeholder="请选择关联分中心" style="width:100%" clearable>
          <el-option v-for="item in subCenterOptions" :key="item.value" :label="item.label" :value="item.value" />
        </el-select>
      </el-form-item>
      <el-form-item label="备注" prop="remark">
        <el-input v-model="form.remark" type="textarea" placeholder="请输入备注" />
      </el-form-item>
    </el-form>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="handleClose" :disabled="isSubmitting">取 消</el-button>
        <el-button type="primary" @click="submitForm" :loading="isSubmitting">确 定</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, reactive, watch, computed } from 'vue';
import { ElMessage } from 'element-plus';
import API from '@/api/maintainance';
import _ from 'lodash';
import { validateNoWhitespace } from '@/utils/validateRule';
import { useDictStore } from '@/stores/modules/dict';
import _D from '@/edata/_osp_data';

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  data: {
    type: Object,
    default: () => ({})
  },
})

const emit = defineEmits(['update:visible', 'success']);

const dictStore = useDictStore();
const dialogVisible = ref(false);
const formRef = ref(null);
const isSubmitting = ref(false);

const inspectionTypeOptions = computed(() => dictStore.getDictByType('inspection_type'));
const stationTypeOptions = computed(() => dictStore.getDictByType('station_type'));
const configOptions = ref([])
const subCenterOptions = _D.subCenterList;


const form = reactive({
  id: '',
  planName: '',
  configId: '',
  inspectionType: '',
  stationType: '',
  startDate: '',
  endDate: '',
  subCenterCodeList: [],
  remark: ''
});

const disabledDate = (time) => {
  return time.getTime() < Date.now();
};

const validateEndDate = (rule, value, callback) => {
  if (form.startDate && value && new Date(value) < new Date(form.startDate)) {
    callback(new Error('结束日期不能早于开始日期'));
  } else {
    callback();
  }
};

const rules = reactive({
  planName: [
    { required: true, message: '请输入计划名称', trigger: 'blur' },
    { max: 50, message: '计划名称不能超过50个字符', trigger: 'blur' },
    { validator: validateNoWhitespace, trigger: 'blur' }
  ],
  configId: [
    { required: true, message: '请选择巡检配置', trigger: 'change' },
  ],
  inspectionType: [
    { required: true, message: '请选择巡检类型', trigger: 'change' },
  ],
  stationType: [
    { required: true, message: '请选择电站类型', trigger: 'change' },
  ],
  startDate: [
    { required: true, message: '请选择计划开始日期', trigger: 'change' },
  ],
  endDate: [
    { required: true, message: '请选择计划结束日期', trigger: 'change' },
    { validator: validateEndDate, trigger: 'change' }
  ],
  subCenterCodeList: [
    { required: true, message: '请选择分中心', trigger: 'change' },
  ],
  remark: [
    { max: 500, message: '备注不能超过500个字符', trigger: 'blur' },
  ]
});

watch(() => props.visible, (val) => {
  dialogVisible.value = val;
  if (val) {
    if (props.data) {
      API.getInspectionConfigList({
        stationType: props.data.stationType,
        configType: props.data.inspectionType,
      }).then(res => {
        configOptions.value = res.result.map(item => {
          return {
            label: item.configName,
            value: item.id
          }
        })
      })
    }
    if (props.data && props.data.id) {
      Object.assign(form, _.cloneDeep(props.data));
      if (form.subCenterCodeList && !Array.isArray(form.subCenterCodeList)) {
        form.subCenterCodeList = [form.subCenterCodeList];
      } else if (!form.subCenterCodeList) {
        form.subCenterCodeList = [];
      }
    } else {
      Object.assign(form, {
        id: '',
        planName: '',
        configId: '',
        inspectionType: props.data?.inspectionType,
        stationType: props.data?.stationType,
        startDate: '',
        endDate: '',
        subCenterCodeList: [],
        remark: ''
      });
    }
    formRef.value?.clearValidate();
  }
}, { immediate: true });

const handleStationChange = (val) => {
  form.configId = ''
  API.getInspectionConfigList({
    stationType: val,
    configType: form.inspectionType,
  }).then(res => {
    configOptions.value = res.result.map(item => {
      return {
        label: item.configName,
        value: item.id
      }
    })
  })
}

const submitForm = async () => {
  if (isSubmitting.value) return;
  if (!formRef.value) return;

  const valid = await formRef.value.validate();
  if (!valid) return;

  isSubmitting.value = true;
  try {
    const formData = _.cloneDeep(form);

    const res = formData.id
      ? await API.updateInspectionPlan(formData)
      : await API.createInspectionPlan(formData);

    if (!res?.data?.success) {
      ElMessage.error(res?.data?.error || res?.data?.message || '保存失败');
      return;
    }

    ElMessage.success('保存成功');
    emit('success');
    handleClose();
  } catch (error) {
    console.error('保存巡检计划失败:', error);
    ElMessage.error(error?.data?.error || error?.data?.message || '系统异常，请稍后重试');
  } finally {
    isSubmitting.value = false;
  }
};

const handleClose = () => {
  emit('update:visible', false);
  Object.assign(form, {
    id: '',
    planName: '',
    configId: '',
    inspectionType: '',
    stationType: '',
    startDate: '',
    endDate: '',
    subCenterCodeList: [],
    remark: ''
  });
  formRef.value?.clearValidate();
};
</script>

<style lang="less" scoped>
.dialog-footer {
  text-align: right;
}
</style>
