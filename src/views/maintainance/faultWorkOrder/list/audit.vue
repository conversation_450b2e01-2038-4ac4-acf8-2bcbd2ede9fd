<template>
	<div style="height: 100%;">
		<el-tabs v-model="activeTab">
			<el-tab-pane label="未下发" name="dispatch"></el-tab-pane>
			<el-tab-pane label="待审核" name="approve"></el-tab-pane>
			<el-tab-pane label="已审核" name="approved"></el-tab-pane>
		</el-tabs>
		<div class="wrap">
			<div class="cus-header">
				<el-form :model="formSearch" label-width="100px">
					<!-- 基础查询条件 -->
						<div class="form-item-grid" :class="[isExpanded ? 'is-expanded' : '']">
							<el-form-item label="运维单号">
								<el-input v-model="formSearch.orderCode" placeholder="输入运维单号" clearable />
							</el-form-item>

							<el-form-item label="电站编码">
								<el-input v-model="formSearch.stationCode" placeholder="输入电站编码" clearable />
							</el-form-item>

							<el-form-item label="运维商">
								<el-input v-model="formSearch.opName" placeholder="输入运维商" clearable />
							</el-form-item>

							<el-form-item label="电站名称">
								<el-input v-model="formSearch.stationName" placeholder="输入电站名称" clearable />
							</el-form-item>

							<el-form-item label="逆变器SN码">
								<el-input v-model="formSearch.inverterSn" placeholder="输入逆变器SN码" clearable />
							</el-form-item>

							<el-form-item label="分中心">
								<el-select v-model="formSearch.subCenterCode" placeholder="选择分中心" clearable style="width: 100%;">
									<el-option v-for="item in subCenterOptions" :key="item.value" :label="item.label" :value="item.value" />
								</el-select>
							</el-form-item>

							<el-form-item label="运维商类别">
								<el-select v-model="formSearch.opType" placeholder="选择运维商类别" clearable style="width: 100%;">
									<el-option v-for="item in opTypeOptions" :key="item.value" :label="item.label" :value="item.value" />
								</el-select>
							</el-form-item>

							<el-form-item label="工单来源">
								<el-select v-model="formSearch.orderSource" placeholder="选择工单来源" clearable style="width: 100%;">
									<el-option v-for="item in sourceOptions" :key="item.value" :label="item.label" :value="item.value" />
								</el-select>
							</el-form-item>

							<el-form-item label="工单类型">
								<el-select v-model="formSearch.orderType" placeholder="选择工单类型" clearable style="width: 100%;">
									<el-option v-for="item in workOrderTypeOptions" :key="item.value" :label="item.label" :value="item.value" />
								</el-select>
							</el-form-item>

							<el-form-item label="资方所属">
								<el-select v-model="formSearch.specialFlag" placeholder="选择资方所属" clearable style="width: 100%;">
									<el-option v-for="item in capitalBelongOptions" :key="item.value" :label="item.label" :value="item.value" />
								</el-select>
							</el-form-item>

							<el-form-item label="生成工单时间">
								<el-date-picker v-model="createTime" type="daterange" value-format="YYYY-MM-DD" start-placeholder="开始时间"
									end-placeholder="结束时间" @change="dateChange" clearable style="width: 100%;" />
							</el-form-item>

							<!-- 查询按钮组 -->
							<div class="search-buttons">
								<el-button type="default" @click="onReset">重置</el-button>
								<el-button type="primary" @click="queryList">查询</el-button>
								<el-link type="primary" @click="toggleExpand" :underline="false" style="margin-left: 10px;">
									{{ isExpanded ? '收起' : '展开' }}
									<el-icon>
										<arrow-up v-if="isExpanded" />
										<arrow-down v-else />
									</el-icon>
								</el-link>
							</div>
						</div>
				</el-form>
			</div>
			<div class="cus-main" ref="mainRef">
				<div class="cus-list" v-loading="loading" ref="cusListRef">
					<div style="text-align: right;">
						<el-button type="success" plain @click="exportList">导出</el-button>
					</div>
					<el-table :data="listArr" class="cus-table">
						<el-table-column fixed align="center" type="index" label="序号" width="60" />
						<el-table-column fixed align="center" prop="orderCode" label="运维单号" width="200" />
						<el-table-column align="center" prop="stationCode" label="电站编码" width="200">
							<template #default="scope">
								{{ scope.row.stationCode || '-' }}
							</template>
						</el-table-column>
						<el-table-column align="center" prop="inverterSn" label="逆变器SN码" width="150">
							<template #default="scope">
								{{ scope.row.inverterSn || '-' }}
							</template>
						</el-table-column>
						<el-table-column align="center" prop="orderSourceName" label="工单来源" width="120">
							<template #default="scope">
								{{ scope.row.orderSourceName || '-' }}
							</template>
						</el-table-column>
						<el-table-column align="center" prop="orderTypeName" label="工单类型" width="120">
							<template #default="scope">
								{{ scope.row.orderTypeName || '-' }}
							</template>
						</el-table-column>
						<el-table-column align="center" prop="faultPhenomenon" label="故障现象" width="150">
							<template #default="scope">
								{{ scope.row.faultPhenomenon || '-' }}
							</template>
						</el-table-column>
						<el-table-column align="center" prop="modeName" label="模式" width="120">
							<template #default="scope">
								{{ scope.row.modeName || '-' }}
							</template>
						</el-table-column>
						<el-table-column align="center" prop="assetBelongName" label="资产所属" width="120">
							<template #default="scope">
								{{ scope.row.assetBelongName || '-' }}
							</template>
						</el-table-column>
						<el-table-column align="center" prop="stationTypeName" label="电站类型" width="120">
							<template #default="scope">
								{{ scope.row.stationTypeName || '-' }}
							</template>
						</el-table-column>
						<el-table-column align="center" prop="projectCompanyName" label="所属项目公司" width="150">
							<template #default="scope">
								{{ scope.row.projectCompanyName || '-' }}
							</template>
						</el-table-column>
						<el-table-column align="center" prop="opName" label="运维商" width="150">
							<template #default="scope">
								{{ scope.row.opName || '-' }}
							</template>
						</el-table-column>
						<el-table-column align="center" prop="opTypeName" label="运维商类别" width="120">
							<template #default="scope">
								{{ scope.row.opTypeName || '-' }}
							</template>
						</el-table-column>
						<el-table-column align="center" prop="subCenterName" label="所属分中心" width="120">
							<template #default="scope">
								{{ scope.row.subCenterName || '-' }}
							</template>
						</el-table-column>
						<el-table-column align="center" prop="stationOwnerName" label="电站业主" width="120">
							<template #default="scope">
								{{ scope.row.stationOwnerName || '-' }}
							</template>
						</el-table-column>
						<el-table-column align="center" prop="ownerContact" label="业主联系方式" width="150">
							<template #default="scope">
								{{ scope.row.ownerContact || '-' }}
							</template>
						</el-table-column>
						<el-table-column align="center" prop="regionName" label="区域" width="120">
							<template #default="scope">
								{{ scope.row.regionName || '-' }}
							</template>
						</el-table-column>
						<el-table-column align="center" prop="detailedAddress" label="详细地址" width="200">
							<template #default="scope">
								{{ scope.row.detailedAddress || '-' }}
							</template>
						</el-table-column>
						<el-table-column align="center" prop="isWarrantyPeriod" label="是否在质保期" width="120">
							<template #default="scope">
								{{ scope.row.isWarrantyPeriod ? '是' : '否' }}
							</template>
						</el-table-column>
						<el-table-column align="center" prop="opBusinessTypeName" label="运维业务类型" width="150">
							<template #default="scope">
								{{ scope.row.opBusinessTypeName || '-' }}
							</template>
						</el-table-column>
						<el-table-column fixed="right" align="center" label="操作" width="160">
							<template #default="scope">
								<el-button link type="primary" @click="dealOrder(scope.row)">处理</el-button>
							</template>
						</el-table-column>
					</el-table>
					<el-pagination class="cus-pages" v-if="total" background layout="sizes, prev, pager, next, ->, total"
						:page-sizes="[10, 20, 30]" :page-size="pagination.pageSize" :current-page="pagination.pageNum"
						:total="total" @size-change="changeSize" @current-change="changeCurrent" />
				</div>
			</div>
		</div>
	</div>
</template>

<script setup>
import { ref, reactive, onMounted, watch } from 'vue';
import { useRouter } from 'vue-router';
import API from '@/api/maintainance';
import _ from 'lodash';
import { ArrowDown, ArrowUp } from '@element-plus/icons-vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import { useTablePagination } from '@/composables/useTablePagination';

const isExpanded = ref(false);
const activeTab = ref('deal');
const createTime = ref('');
const router = useRouter();

const apiMap = {
  'dispatch': API.getWorkOrderToDispatchPage,
  'approve': API.getWorkOrderToApprovePage,
  'approved': API.getWorkOrderToApprovedPage,
}

const formSearch = reactive({
	createTimeEnd: null,
	createTimeStart: null,
	orderCode: '',
	stationCode: '',
	opName: '',
	stationName: '',
	inverterSn: '',
	subCenterCode: '',
	opType: '',
	orderSource: '',
	orderType: '',
	specialFlag: '',
});

// 使用组合式函数管理表格和分页状态
const {
	loading,
	listArr,
	total,
	pagination,
	getList,
	queryList,
	changeSize,
	changeCurrent,
} = useTablePagination(
	API.getWorkOrderToDispatchPage,
	() => formSearch,
	{ manual: true }
);

// 日期选择
const dateChange = (e) => {
	if (e) {
		formSearch.createTimeStart = e[0];
		formSearch.createTimeEnd = e[1];
	} else {
		formSearch.createTimeStart = null;
		formSearch.createTimeEnd = null;
	}
};

// 重置筛选条件
const onReset = _.throttle(
	() => {
		createTime.value = '';
		Object.assign(formSearch, {
			createTimeEnd: null,
			createTimeStart: null,
			orderCode: '',
			stationCode: '',
			opName: '',
			stationName: '',
			inverterSn: '',
			subCenterCode: '',
			opType: '',
			orderSource: '',
			orderType: '',
			specialFlag: ''
		});
		queryList();
	},
	3000,
	{
		trailing: false
	}
);

// 导出
const exportList = _.throttle(
	() => {
		ElMessageBox.confirm('请确认是否导出此筛选条件下的列表?', '提示', {
			confirmButtonText: '确定',
			cancelButtonText: '取消',
			type: 'warning'
		}).then(() => {
			const params = _.cloneDeep(formSearch);
			API.repairReportdoExport(params)
				.then(res => {
					let binaryData = [];
					let link = document.createElement('a');
					binaryData.push(res);
					link.style.display = 'none';
					link.href = window.URL.createObjectURL(new Blob(binaryData));
					link.setAttribute('download', '业主报修列表.xlsx');
					document.body.appendChild(link);
					link.click();
					document.body.removeChild(link);
				})
				.finally(() => {
					loading.close();
				});
		});
	},
	3000,
	{
		trailing: false
	}
);

// 切换展开/收起
const toggleExpand = () => {
	isExpanded.value = !isExpanded.value;
};

// 处理工单
const dealOrder = (row) => {
	router.push({
		path: '/maintainance/faultWorkOrder/detail',
		query: {
			repairOrderSn: row.repairOrderSn
		}
	});
};

// 监听标签页切换
watch(activeTab, (newVal) => {
	queryList();
});

onMounted(() => {
	getList();
});
</script>

<style lang="less" scoped>
@import '@/assets/style/_cus_header.less';
@import '@/assets/style/_cus_list.less';

:deep(.el-tabs__nav-scroll) {
	background-color: var(--el-bg-color);
	padding-left: 12px;
}

.wrap {
	height: calc(100% - 54px);
}

.cus-header {
	margin-bottom: 0px;

	.form-item-grid {
		display: grid;
		grid-template-columns: repeat(3, 1fr);
		gap: 12px;
		width: 100%;

		/* 默认状态（收起）- 只显示前两个元素和按钮组 */
		.el-form-item:nth-child(n+3):not(:last-child) {
			display: none;
		}

		/* 展开状态 - 显示所有元素 */
		&.is-expanded {
			.el-form-item:nth-child(n+3):not(:last-child) {
				display: flex;
			}
		}
	}

	.search-buttons {
		display: flex;
		align-items: center;
		justify-content: flex-end;
		grid-column: -2 / -1;
	}

	.el-form-item {
		width: 100%;
		margin-bottom: 0px;

		.el-input,
		.el-select,
		.el-date-editor {
			width: 100%;
		}
	}
}

.cus-main {
	display: flex;
	flex-direction: column;
	flex: 1;
	overflow: hidden;

	>.el-button {
		margin-bottom: 10px;
	}

	.cus-list {
		display: flex;
		flex-direction: column;
		flex: 1;
		overflow: hidden;
		position: relative;

		.cus-table {
			flex: 1;
			overflow: auto;
			height: 100%;
		}

		.cus-pages {
			margin-top: 10px;
		}
	}
}
</style>
