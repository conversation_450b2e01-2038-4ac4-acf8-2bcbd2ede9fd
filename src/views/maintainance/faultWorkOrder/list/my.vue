<template>
	<div class="wrap">
		<div class="cus-header">
			<el-form :model="formSearch" label-width="100px">
				<!-- 基础查询条件 -->
				<div class="form-item-grid" :class="[isExpanded ? 'is-expanded' : '']">
					<el-form-item label="运维单号">
						<el-input v-model="formSearch.orderCode" placeholder="输入运维单号" clearable />
					</el-form-item>

					<el-form-item label="电站编码">
						<el-input v-model="formSearch.stationCode" placeholder="输入电站编码" clearable />
					</el-form-item>

					<el-form-item label="运维商">
						<el-input v-model="formSearch.opName" placeholder="输入运维商" clearable />
					</el-form-item>

					<el-form-item label="电站名称">
						<el-input v-model="formSearch.stationName" placeholder="输入电站名称" clearable />
					</el-form-item>

					<el-form-item label="逆变器SN码">
						<el-input v-model="formSearch.inverterSn" placeholder="输入逆变器SN码" clearable />
					</el-form-item>

					<el-form-item label="分中心">
						<el-select v-model="formSearch.subCenterCode" placeholder="选择分中心" clearable style="width: 100%;">
							<el-option v-for="item in subCenterOptions" :key="item.value" :label="item.label" :value="item.value" />
						</el-select>
					</el-form-item>

					<el-form-item label="运维商类别">
						<el-select v-model="formSearch.opType" placeholder="选择运维商类别" clearable style="width: 100%;">
							<el-option v-for="(item, val) in opTypeOptions" :label="item" :value="val" :key="val" />
						</el-select>
					</el-form-item>

					<el-form-item label="工单类型">
						<el-select v-model="formSearch.orderType" placeholder="选择工单类型" clearable style="width: 100%;">
							<el-option v-for="item in workOrderTypeOptions" :key="item.value" :label="item.label"
								:value="item.value" />
						</el-select>
					</el-form-item>

					<el-form-item label="工单来源">
						<el-select v-model="formSearch.orderSource" placeholder="选择工单来源" clearable style="width: 100%;">
							<el-option v-for="item in sourceOptions" :key="item.value" :label="item.label" :value="item.value" />
						</el-select>
					</el-form-item>

					<el-form-item label="资方所属">
						<el-select v-model="formSearch.specialFlag" placeholder="选择资方所属" clearable style="width: 100%;">
							<el-option v-for="item in capitalBelongOptions" :key="item.value" :label="item.label"
								:value="item.value" />
						</el-select>
					</el-form-item>

					<el-form-item v-if="false" label="工单状态"> <!-- my.vue 中似乎不需要此筛选，暂时隐藏 -->
						<el-select v-model="formSearch.orderStatus" placeholder="选择工单状态" clearable style="width: 100%;">
							<el-option v-for="item in workOrderStatusOptions" :key="item.value" :label="item.label"
								:value="item.value" />
						</el-select>
					</el-form-item>

					<el-form-item label="生成工单时间">
						<el-date-picker v-model="createTime" type="daterange" value-format="YYYY-MM-DD" start-placeholder="开始时间"
							end-placeholder="结束时间" @change="dateChange" clearable style="width: 100%;" />
					</el-form-item>

					<!-- 查询按钮组 -->
					<div class="search-buttons">
						<el-button type="default" @click="onReset">重置</el-button>
						<el-button type="primary" @click="queryList">查询</el-button>
						<el-link type="primary" @click="toggleExpand" :underline="false" style="margin-left: 10px;">
							{{ isExpanded ? '收起' : '展开' }}
							<el-icon>
								<arrow-up v-if="isExpanded" />
								<arrow-down v-else />
							</el-icon>
						</el-link>
					</div>
				</div>
			</el-form>
		</div>
		<div class="cus-main" ref="mainRef">
			<div class="cus-list" v-loading="loading" ref="cusListRef">
				<div style="text-align: right;">
					<el-button type="success" plain @click="openCreateDrawer">新建</el-button>
				</div>
				<el-table :data="listArr" class="cus-table">
					<el-table-column fixed align="center" type="index" label="序号" width="60" />
					<el-table-column fixed align="center" prop="orderCode" label="运维单号" width="200" />
					<el-table-column align="center" prop="stationCode" label="电站编码" width="200">
						<template #default="scope">
							{{ scope.row.stationCode || '-' }}
						</template>
					</el-table-column>
					<el-table-column align="center" prop="inverterSn" label="逆变器SN码" width="150">
						<template #default="scope">
							{{ scope.row.inverterSn || '-' }}
						</template>
					</el-table-column>
					<el-table-column align="center" prop="orderSource" label="工单来源" width="120">
						<template #default="scope">
							{{ getDictLabel('work_order_source', scope.row.orderSource) }}
						</template>
					</el-table-column>
					<el-table-column align="center" prop="orderType" label="工单类型" width="120">
						<template #default="scope">
							{{ getDictLabel('work_order_type', scope.row.orderType) }}
						</template>
					</el-table-column>
					<el-table-column align="center" prop="orderStatus" label="工单状态" width="120">
						<template #default="scope">
							{{ getDictLabel('work_order_status', scope.row.orderStatus) }}
						</template>
					</el-table-column>
					<el-table-column align="center" prop="faultDescription" label="故障现象" width="150">
						<template #default="scope">
							{{ scope.row.faultDescription || '-' }}
						</template>
					</el-table-column>
					<el-table-column align="center" prop="mode" label="模式" width="120">
						<template #default="scope">
							{{ scope.row.mode || '-' }}
						</template>
					</el-table-column>
					<el-table-column align="center" prop="specialFlag" label="资产所属" width="120">
						<template #default="scope">
							{{ pmSpecialFlag[scope.row.specialFlag] || '-' }}
						</template>
					</el-table-column>
					<el-table-column align="center" prop="stationType" label="电站类型" width="120">
						<template #default="scope">
							{{ detStationType[scope.row.stationType] || '-' }}
						</template>
					</el-table-column>
					<el-table-column align="center" prop="projectCompanyName" label="所属项目公司" width="150">
						<template #default="scope">
							{{ scope.row.projectCompanyName || '-' }}
						</template>
					</el-table-column>
					<el-table-column align="center" prop="opName" label="运维商" width="150">
						<template #default="scope">
							{{ scope.row.opName || '-' }}
						</template>
					</el-table-column>
					<!-- <el-table-column align="center" prop="opType" label="运维商类别" width="120">
							<template #default="scope">
								{{ identityType[scope.row.opType] || '-' }}
							</template>
						</el-table-column> -->
					<el-table-column align="center" prop="subCenterName" label="所属分中心" width="120">
						<template #default="scope">
							{{ scope.row.subCenterName || '-' }}
						</template>
					</el-table-column>
					<el-table-column align="center" prop="stationName" label="电站业主" width="120">
						<template #default="scope">
							{{ scope.row.stationName || '-' }}
						</template>
					</el-table-column>
					<el-table-column align="center" prop="stationPhone" label="业主联系方式" width="150">
						<template #default="scope">
							{{ scope.row.stationPhone || '-' }}
						</template>
					</el-table-column>
					<el-table-column align="center" prop="regionName" label="区域" width="120">
						<template #default="scope">
							{{ scope.row.regionName || '-' }}
						</template>
					</el-table-column>
					<el-table-column align="center" prop="address" label="详细地址" width="200">
						<template #default="scope">
							{{ scope.row.address || '-' }}
						</template>
					</el-table-column>
					<el-table-column align="center" prop="isWarranty" label="是否在质保期" width="120">
						<template #default="scope">
							{{ isWarranty[scope.row.isWarranty] || '-' }}
						</template>
					</el-table-column>
					<el-table-column align="center" prop="opBusinessType" label="运维业务类型" width="150">
						<template #default="scope">
							{{ businessType[scope.row.opBusinessType] || '-' }}
						</template>
					</el-table-column>
					<el-table-column fixed="right" align="center" label="操作" width="120">
						<template #default="scope">
							<el-button link type="primary" @click="viewOrder(scope.row)">查看</el-button>
							<el-button v-if="['TO_HEAD_DISPATCH', 'TO_SUB_CENTER_DISPATCH'].includes(scope.row.orderStatus)" link type="primary" @click="closeOrder(scope.row)">关单</el-button>
						</template>
					</el-table-column>
				</el-table>
				<el-pagination class="cus-pages" v-if="total" background layout="sizes, prev, pager, next, ->, total"
					:page-sizes="[10, 20, 30]" :page-size="pagination.pageSize" :current-page="pagination.pageNum" :total="total"
					@size-change="changeSize" @current-change="changeCurrent" />
			</div>
		</div>

		<operate-drawer v-model:visible="operateDrawerVisible" :data="selectedRowData" @success="handleOperateSuccess" />
		<CloseOrderDialog v-model:visible="closeOrderDialogVisible" :orderCode="currentOrderCode"
			@closed="handleOrderClosedInList" />
	</div>
</template>

<script setup>
import { ref, reactive, onMounted, watch, computed } from 'vue';
import { useRouter } from 'vue-router';
import API from '@/api/maintainance';
import _ from 'lodash';
import { ArrowDown, ArrowUp } from '@element-plus/icons-vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import _D from '@/edata/_osp_data';
import { useDictStore } from '@/stores/modules/dict';
import { useTablePagination } from '@/composables/useTablePagination';
import OperateDrawer from './components/Operate.vue';
import CloseOrderDialog from '../components/CloseOrderDialog.vue';

// 响应式状态
const isExpanded = ref(false);
const operateDrawerVisible = ref(false);
const selectedRowData = ref({});
const closeOrderDialogVisible = ref(false);
const currentOrderCode = ref(null);
const createTime = ref('');
const router = useRouter();
const dictStore = useDictStore();

const sourceOptions = computed(() => dictStore.getDictByType('work_order_source'));
const workOrderTypeOptions = computed(() => dictStore.getDictByType('work_order_type'));

const getDictLabel = (dictType, value) => {
	const dictMap = dictStore.getDictMapByType(dictType);
	return dictMap[value] || '-';
};

const subCenterOptions = _D.subCenterList;
const opTypeOptions = _D.identityType;
const capitalBelongOptions = _D.property;
const pmSpecialFlag = _D.pmSpecialFlag;
const businessType = _D.businessType;
const isWarranty = _D.isWarranty;
const detStationType = _D.detStationType;
const identityType = _D.identityType;

const formSearch = reactive({
	createTimeEnd: null,
	createTimeStart: null,
	orderCode: '',
	stationCode: '',
	opName: '',
	stationName: '',
	inverterSn: '',
	subCenterCode: '',
	opType: '',
	orderSource: '',
	orderType: '',
	specialFlag: ''
});

// 使用组合式函数管理表格和分页状态
const {
	loading,
	listArr,
	total,
	pagination,
	getList,
	queryList,
	changeSize,
	changeCurrent,
} = useTablePagination(
	API.getMySubmittedWorkOrderPage,
	() => formSearch,
	{ manual: true }
);

// 日期选择
const dateChange = (e) => {
	if (e) {
		formSearch.createTimeStart = e[0];
		formSearch.createTimeEnd = e[1];
	} else {
		formSearch.createTimeStart = null;
		formSearch.createTimeEnd = null;
	}
};

// 重置筛选条件
const onReset = _.throttle(
	() => {
		createTime.value = '';
		Object.assign(formSearch, {
			createTimeEnd: null,
			createTimeStart: null,
			orderCode: '',
			stationCode: '',
			opName: '',
			stationName: '',
			inverterSn: '',
			subCenterCode: '',
			opType: '',
			orderSource: '',
			orderType: '',
			specialFlag: ''
		});
		queryList();
	},
	3000,
	{
		trailing: false
	}
);

const viewOrder = (row) => {
	router.push({
		path: '/maintainance/faultWorkOrder/detail',
		query: {
			orderCode: row.orderCode,
			action: 'view'
		}
	});
};

const closeOrder = (row) => {
	if (!row.id) {
		ElMessage.warning('无法获取工单ID，操作失败');
		return;
	}
	currentOrderCode.value = row.orderCode;
	closeOrderDialogVisible.value = true;
};

// 关单成功后的回调函数
const handleOrderClosedInList = () => {
	closeOrderDialogVisible.value = false;
	currentOrderCode.value = null;
	queryList(); 
};

const openCreateDrawer = (row = null) => {
	if (row && row.id) { 
		selectedRowData.value = _.cloneDeep(row);
	} else {
		selectedRowData.value = {};
	}
	operateDrawerVisible.value = true;
};

const handleOperateSuccess = () => {
	operateDrawerVisible.value = false;
	queryList();
};

// 切换展开/收起
const toggleExpand = () => {
	isExpanded.value = !isExpanded.value;
};


onMounted(() => {
	getList();
	dictStore.fetchDict([
		'work_order_type',
		'work_order_source',
		'work_order_status',
		'close_order_reason',
	]);
});
</script>

<style lang="less" scoped>
@import '@/assets/style/_cus_header.less';
@import '@/assets/style/_cus_list.less';

.wrap {
	height: 100%;
}

.cus-header {
	margin-bottom: 0px;

	.form-item-grid {
		display: grid;
		grid-template-columns: repeat(3, 1fr);
		gap: 12px;
		width: 100%;

		/* 默认状态（收起）- 只显示前两个元素和按钮组 */
		.el-form-item:nth-child(n+3):not(:last-child) {
			display: none;
		}

		/* 展开状态 - 显示所有元素 */
		&.is-expanded {
			.el-form-item:nth-child(n+3):not(:last-child) {
				display: flex;
			}
		}
	}

	.search-buttons {
		display: flex;
		align-items: center;
		justify-content: flex-end;
		grid-column: -2 / -1;
	}

	.el-form-item {
		width: 100%;
		margin-bottom: 0px;

		.el-input,
		.el-select,
		.el-date-editor {
			width: 100%;
		}
	}
}

.cus-main {
	display: flex;
	flex-direction: column;
	flex: 1;
	overflow: hidden;

	>.el-button {
		margin-bottom: 10px;
	}

	.cus-list {
		display: flex;
		flex-direction: column;
		flex: 1;
		overflow: hidden;
		position: relative;

		.cus-table {
			flex: 1;
			overflow: auto;
			height: 100%;
		}

		.cus-pages {
			margin-top: 10px;
		}
	}
}
</style>
