<template>
  <el-dialog v-model="dialogVisible" title="驳回工单" width="500px" :close-on-click-modal="false" @close="handleClose">
    <el-form ref="formRef" :model="form" :rules="rules" label-width="100px">
      <el-form-item label="驳回原因" prop="rejectReason">
        <el-select v-model="form.rejectReason" placeholder="请选择驳回原因" style="width: 100%;">
          <el-option v-for="item in rejectReasons" :key="item.value" :label="item.label" :value="item.value" />
        </el-select>
      </el-form-item>
      <el-form-item label="驳回备注" prop="rejectDesc">
        <el-input v-model="form.rejectDesc" type="textarea" :rows="3" placeholder="请输入驳回备注" />
      </el-form-item>
    </el-form>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="handleClose" :disabled="isSubmitting">取消</el-button>
        <el-button type="primary" @click="submitForm" :loading="isSubmitting">确定</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, reactive, watch, computed } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import API from '@/api/maintainance';
import { useDictStore } from '@/stores/modules/dict';

const props = defineProps({
  visible: {
    type: Boolean,
    default: false,
  },
  orderCode: {
    type: [String, Number],
    required: true,
  },
});

const dictStore = useDictStore();
const rejectReasons = computed(() => dictStore.getDictByType('audit_reject_reason'));

const emit = defineEmits(['update:visible', 'rejected']);

const dialogVisible = ref(props.visible);
const formRef = ref(null);
const form = reactive({
  rejectReason: '',
  rejectDesc: '',
});
const isSubmitting = ref(false);

const rules = reactive({
  rejectReason: [{ required: true, message: '请选择驳回原因', trigger: 'change' }],
  rejectDesc: [{ max: 500, message: '备注不能超500个字符', trigger: 'blur' }],
});

watch(() => props.visible, (newVal) => {
  dialogVisible.value = newVal;
  if (newVal) {
    form.rejectReason = '';
    form.rejectDesc = '';
    if (formRef.value) {
      formRef.value.resetFields();
    }
  }
});

const handleClose = () => {
  emit('update:visible', false);
};

const submitForm = async () => {
  if (!formRef.value) return;
  await formRef.value.validate(async (valid) => {
    if (valid) {
      try {
        await ElMessageBox.confirm('确认要驳回此工单吗?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning',
        });

        isSubmitting.value = true;
        const params = {
          orderCode: props.orderCode,
          rejectReason: form.rejectReason,
          rejectDesc: form.rejectDesc,
        };
       
        const res = await API.auditRejectWorkOrder(params); 
        if (res.data.success) {
          ElMessage.success('工单驳回成功');
          emit('rejected');
          handleClose();
        } else {
          ElMessage.error(res.data.error || '驳回失败');
        }
      } catch (error) {
        if (error !== 'cancel') {
          console.error('驳回操作失败:', error);
          ElMessage.error('驳回操作时发生错误');
        }
      } finally {
        isSubmitting.value = false;
      }
    } else {
      ElMessage.error('请完成必填项');
      return false;
    }
  });
};
</script>

<style scoped>
.dialog-footer {
  text-align: right;
}
</style>