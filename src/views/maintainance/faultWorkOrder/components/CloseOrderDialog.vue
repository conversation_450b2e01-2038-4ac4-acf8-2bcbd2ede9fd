<template>
  <el-dialog v-model="dialogVisible" title="关单确认" width="500px" :close-on-click-modal="false" @close="handleClose">
    <el-form :model="closeOrderForm" :rules="rules" label-width="100px" ref="closeOrderFormRef">
      <el-form-item label="关单原因" prop="closeReason">
        <el-select v-model="closeOrderForm.closeReason" placeholder="请选择关单原因" style="width: 100%;">
          <el-option v-for="item in closeReasons" :key="item.value" :label="item.label" :value="item.value" />
        </el-select>
      </el-form-item>
      <el-form-item label="关单备注" prop="closeDesc">
        <el-input v-model="closeOrderForm.closeDesc" type="textarea" :rows="3" placeholder="请输入关单备注" />
      </el-form-item>
    </el-form>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="handleClose" :disabled="isSubmitting">取消</el-button>
        <el-button type="primary" @click="submitCloseOrder" :loading="isSubmitting">确定</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, reactive, watch, computed } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import API from '@/api/maintainance';
import { useDictStore } from '@/stores/modules/dict';

const props = defineProps({
  visible: {
    type: Boolean,
    default: false,
  },
  orderCode: {
    type: [String, Number],
    required: true,
  },
});

const dictStore = useDictStore();
const closeReasons = computed(() => dictStore.getDictByType('close_order_reason'));

const rules = reactive({
  closeReason: [{ required: true, message: '请选择关单原因', trigger: 'change' }],
  closeDesc: [{ max: 500, message: '备注不能超500个字符', trigger: 'blur' }],
});

const emit = defineEmits(['update:visible', 'closed']);

const dialogVisible = ref(props.visible);
const closeOrderForm = reactive({
  closeDesc: '',
  closeReason: '',
});
const closeOrderFormRef = ref(null);
const isSubmitting = ref(false);

watch(() => props.visible, (newVal) => {
  dialogVisible.value = newVal;
  if (newVal) {
    closeOrderForm.closeDesc = '';
    closeOrderForm.closeReason = '';
    if (closeOrderFormRef.value) {
      closeOrderFormRef.value.resetFields();
    }
  }
});

const handleClose = () => {
  emit('update:visible', false);
};

const submitCloseOrder = async () => {
  if (!closeOrderFormRef.value) return;

  closeOrderFormRef.value.validate(async (valid) => {
    if (valid) {
      try {
        await ElMessageBox.confirm('确认要关闭此工单吗?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning',
        });

        isSubmitting.value = true;
        const params = {
          orderCode: props.orderCode,
          closeReason: closeOrderForm.closeReason,
          closeDesc: closeOrderForm.closeDesc,
        };
        const res = await API.closeWorkOrder(params);
        if (res.data.success) {
          ElMessage.success('工单关闭成功');
          emit('closed');
          handleClose();
        } else {
          ElMessage.error(res.data.error || '关单失败');
        }
      } catch (error) {
        if (error !== 'cancel') {
          console.error('关单操作失败:', error);
          ElMessage.error('关单操作时发生错误');
        }
      } finally {
        isSubmitting.value = false;
      }
    } else {
      ElMessage.error('请完成必填项');
      return false;
    }
  });
};
</script>