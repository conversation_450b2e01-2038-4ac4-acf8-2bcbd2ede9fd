<template>
  <div class="cus-container">
    <div class="cus-header">
      <el-form :model="formSearch" label-width="80px">
        <div class="form-item-grid" :class="{ 'is-expanded': isFormExpanded }">
          <el-form-item label="工单类型">
            <el-select v-model="formSearch.orderType" placeholder="选择工单类型" clearable style="width: 100%;">
              <el-option v-for="item in workOrderTypeOptions" :key="item.value" :label="item.label"
                :value="item.value" />
            </el-select>
          </el-form-item>
          <el-form-item label="分中心">
            <el-select v-model="formSearch.subCenterCode" placeholder="选择分中心" clearable style="width: 100%;">
              <el-option v-for="item in subCenterOptions" :key="item.value" :label="item.label" :value="item.value" />
            </el-select>
          </el-form-item>
          <el-form-item label="是否超时">
            <el-select v-model="formSearch.overTime" placeholder="选择是否超时" clearable style="width: 100%;">
              <el-option key="yes" label="是" :value="true" />
              <el-option key="no" label="否" :value="false" />
            </el-select>
          </el-form-item>
          <el-form-item label="运维商">
            <el-input v-model="formSearch.opName" clearable placeholder="输入运维商名称" />
          </el-form-item>

          <el-form-item label="创建时间">
            <el-date-picker v-model="formSearch.dateRange" type="daterange" range-separator="至"
              start-placeholder="开始时间" end-placeholder="结束时间" value-format="YYYY-MM-DD" style="width: 100%" />
          </el-form-item>

          <div class="search-buttons">
            <el-button type="default" @click="onReset">重置</el-button>
            <el-button type="primary" @click="queryList">查询</el-button>
            <el-button link type="primary" @click="toggleFormExpansion">
              {{ isFormExpanded ? "收起" : "展开" }}
              <el-icon class="el-icon--right">
                <component :is="isFormExpanded ? 'ArrowUp' : 'ArrowDown'" />
              </el-icon>
            </el-button>
          </div>
        </div>
      </el-form>
    </div>
    <div class="cus-wrap">
      <div class="cus-message">
        <div class="chart-container">
          <div class="chart-item">
            <div id="workOrderRateChart" class="chart"></div>
            <div class="chart-summary">
              工单次数: {{ workOrderStats.totalCount }}次
            </div>
          </div>
          <div class="chart-item">
            <div id="workOrderAuditChart" class="chart"></div>
            <div class="chart-summary">
              审核次数: {{ workOrderStats.auditCount }}次
            </div>
          </div>
        </div>
      </div>
      <div class="wrap">
        <div class="cus-main" ref="mainRef">
          <div class="cus-list">
            <h3 class="chart-title">工单效率</h3>
            <el-table v-loading="loading" :data="listArr" class="cus-table">
              <el-table-column fixed align="center" type="index" label="序号" width="60" />
              <el-table-column align="center" prop="opName" label="运维商名称" min-width="150" show-overflow-tooltip />
              <el-table-column align="center" prop="totalCount" label="工单总数" width="100" />
              <el-table-column align="center" prop="auditRate" label="工单审核率" min-width="180">
                <template #default="scope">
                  <div class="progress-container">
                    <div class="progress-bar">
                      <div class="progress-inner" :style="{ width: scope.row.auditRate + '%', backgroundColor: '#409EFF' }"></div>
                    </div>
                    <span class="progress-text">{{ scope.row.auditRate }}%</span>
                  </div>
                </template>
              </el-table-column>
              <el-table-column align="center" prop="finishRate" label="工单完成率" min-width="180">
                <template #default="scope">
                  <div class="progress-container">
                    <div class="progress-bar">
                      <div class="progress-inner" :style="{ width: scope.row.finishRate + '%', backgroundColor: '#67C23A' }"></div>
                    </div>
                    <span class="progress-text">{{ scope.row.finishRate }}%</span>
                  </div>
                </template>
              </el-table-column>
            </el-table>
            <el-pagination 
              class="cus-pages" 
              v-if="total" 
              background 
              layout="sizes, prev, pager, next, ->, total"
              :page-sizes="[10, 20, 30]" 
              :page-size="pagination.pageSize" 
              :current-page="pagination.pageNum"
              :total="total" 
              @size-change="changeSize" 
              @current-change="changeCurrent" />
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, computed, watch } from "vue"
import _ from "lodash"
import _D from '@/edata/_osp_data';
import { useTablePagination } from "@/composables/useTablePagination"
import { ElMessage } from "element-plus"
import API from "@/api/maintainance"
import * as echarts from 'echarts/core'
import { 
  TitleComponent,
  TooltipComponent,
  LegendComponent
} from 'echarts/components'
import { PieChart } from 'echarts/charts'
import { LabelLayout } from 'echarts/features'
import { CanvasRenderer } from 'echarts/renderers'
import { useDictStore } from '@/stores/modules/dict';


const dictStore = useDictStore();

// 注册必须的组件
echarts.use([
  TitleComponent,
  TooltipComponent,
  LegendComponent,
  PieChart,
  LabelLayout,
  CanvasRenderer
])

const isFormExpanded = ref(false)

const workOrderTypeOptions = computed(() => dictStore.getDictByType('work_order_type'));
const subCenterOptions = _D.subCenterList;
// 工单统计数据
const workOrderStats = reactive({
  totalCount: 0,
  auditCount: 0,
  closedCount: 0,
  pendingCount: 0,
  baseFaultCount: 0,
  constructionFaultCount: 0,
  standardFaultCount: 0,
  auditedCount: 0,
  totalAuditCount: 0
})

// 更新表单搜索字段，适配工单效率统计接口
const formSearch = reactive({
  orderType: null,
  subCenterCode: null,
  overTime: null,
  opName: "", // 运维商名称
  createTimeStart: null, // 创建开始时间
  createTimeEnd: null, // 创建结束时间
  dateRange: [] // 日期范围选择器
})

// 使用useTablePagination获取工单效率数据
const {
  loading,
  listArr,
  total,
  pagination,
  getList,
  changeSize,
  changeCurrent,
} = useTablePagination(
  API.getWorkOrderEfficiencyPage,
  () => {
    const params = { ...formSearch }
    // 处理日期范围
    if (params.dateRange && params.dateRange.length === 2) {
      params.createTimeStart = params.dateRange[0]
      params.createTimeEnd = params.dateRange[1]
    }
    delete params.dateRange
    return params
  },
  { manual: true }
)

const toggleFormExpansion = () => {
  isFormExpanded.value = !isFormExpanded.value
}

// 重置搜索表单
const onReset = () => {
  formSearch.orderType = null
  formSearch.subCenterCode = null
  formSearch.overTime = null
  formSearch.opName = ""
  formSearch.dateRange = []
  formSearch.createTimeStart = null
  formSearch.createTimeEnd = null
  queryList()
}

// 初始化工单闭环率图表
const initWorkOrderRateChart = () => {
  const chartDom = document.getElementById('workOrderRateChart')
  if (!chartDom) return
  
  const myChart = echarts.init(chartDom)
  const option = {
    title: {
      text: '工单闭环率',
      left: 'center',
      top: 0,
      textStyle: {
        fontSize: 16,
        fontWeight: 'normal'
      }
    },
    tooltip: {
      trigger: 'item',
      formatter: '{a} <br/>{b}: {c} ({d}%)',
      confine: true // 确保tooltip不会超出边界
    },
    legend: {
      orient: 'horizontal',
      left: 'center',  // 调整为居中
      top: 25, // 调整到标题下方
      itemGap: 30,  // 增加间距
      itemWidth: 14,
      itemHeight: 14,
      data: [
        {
          name: '未完成',
          icon: 'rect'
        }, 
        {
          name: '已完成',
          icon: 'rect'
        }
      ],
      textStyle: {
        fontSize: 12
      }
    },
    color: ['#409EFF', '#67C23A'], // 按顺序：未完成-蓝色，已完成-绿色
    series: [
      {
        name: '工单闭环率',
        type: 'pie',
        radius: ['40%', '70%'],
        center: ['50%', '60%'], // 将饼图中心点下移，避免与legend重叠
        avoidLabelOverlap: false,
        label: {
          show: false,
          position: 'center'
        },
        emphasis: {
          label: {
            show: true,
            fontSize: '14',
            fontWeight: 'bold'
          }
        },
        labelLine: {
          show: false
        },
        data: [
          { value: workOrderStats.pendingCount, name: '未完成' },
          { value: workOrderStats.closedCount, name: '已完成' }
        ]
      }
    ]
  }
  
  myChart.setOption(option)
  
  // 保存图表实例用于后续resize
  if (!window.chartInstances) {
    window.chartInstances = {};
  }
  window.chartInstances.workOrderRateChart = myChart;
  
  // 监听窗口大小变化
  window.addEventListener('resize', () => {
    myChart.resize()
  })
  
  // 使用ResizeObserver监听容器大小变化
  if (typeof ResizeObserver !== 'undefined') {
    const resizeObserver = new ResizeObserver(() => {
      myChart.resize();
    });
    resizeObserver.observe(chartDom);
  }
}

// 初始化工单审核图表
const initWorkOrderAuditChart = () => {
  const chartDom = document.getElementById('workOrderAuditChart')
  if (!chartDom) return
  
  const myChart = echarts.init(chartDom)
  const option = {
    title: {
      text: '工单审核',
      left: 'center',
      top: 0,
      textStyle: {
        fontSize: 16,
        fontWeight: 'normal'
      }
    },
    tooltip: {
      trigger: 'item',
      formatter: '{a} <br/>{b}: {c} ({d}%)',
      confine: true // 确保tooltip不会超出边界
    },
    legend: {
      orient: 'horizontal',
      left: 'center', // 调整为居中
      top: 25, // 调整到标题下方
      itemGap: 15, // 减小间距以适应三个图例项
      itemWidth: 14,
      itemHeight: 14,
      data: [
        {
          name: '基础工单',
          icon: 'rect'
        }, 
        {
          name: '建设工单',
          icon: 'rect'
        },
        {
          name: '规范工单',
          icon: 'rect'
        }
      ],
      textStyle: {
        fontSize: 12
      }
    },
    color: ['#409EFF', '#FFCC00', '#67C23A'],
    series: [
      {
        name: '工单审核',
        type: 'pie',
        radius: ['40%', '70%'],
        center: ['50%', '60%'], // 将饼图中心点下移，避免与legend重叠
        avoidLabelOverlap: false,
        label: {
          show: false,
          position: 'center'
        },
        emphasis: {
          label: {
            show: true,
            fontSize: '14',
            fontWeight: 'bold'
          }
        },
        labelLine: {
          show: false
        },
        data: [
          { value: workOrderStats.baseFaultCount, name: '基础工单' },
          { value: workOrderStats.constructionFaultCount, name: '建设工单' },
          { value: workOrderStats.standardFaultCount, name: '规范工单' }
        ]
      }
    ]
  }
  
  myChart.setOption(option)
  
  // 保存图表实例用于后续resize
  if (!window.chartInstances) {
    window.chartInstances = {};
  }
  window.chartInstances.workOrderAuditChart = myChart;
  
  // 监听窗口大小变化
  window.addEventListener('resize', () => {
    myChart.resize()
  })
  
  // 使用ResizeObserver监听容器大小变化
  if (typeof ResizeObserver !== 'undefined') {
    const resizeObserver = new ResizeObserver(() => {
      myChart.resize();
    });
    resizeObserver.observe(chartDom);
  }
}

// 初始化图表并更新图表数据
const updateChartsData = async () => {
  try {
    const params = { ...formSearch }
    if (params.dateRange && params.dateRange.length === 2) {
      params.createTimeStart = params.dateRange[0]
      params.createTimeEnd = params.dateRange[1]
    }
    delete params.dateRange
    // 获取效率统计总数据
    const res = await API.getWorkOrderEfficiencyStatistic(params)
    if (res.success && res.result) {
      const data = res.result
      // 更新工单统计数据
      workOrderStats.totalCount = data.totalCount || 0
      workOrderStats.auditCount = data.totalAuditCount || 0
      workOrderStats.auditedCount = data.auditedCount || 0
      workOrderStats.closedCount = data.finishedCount || 0
      workOrderStats.pendingCount = data.totalCount - data.finishedCount || 0
      workOrderStats.totalAuditCount = data.totalAuditCount || 0
      
      // 估计基础工单、建设工单和规范工单的数量（假设数据）
      workOrderStats.baseFaultCount = Math.round(data.totalCount * 0.8)
      workOrderStats.constructionFaultCount = Math.round(data.totalCount * 0.15)
      workOrderStats.standardFaultCount = data.totalCount - workOrderStats.baseFaultCount - workOrderStats.constructionFaultCount
      
      // 重新初始化图表
      initWorkOrderRateChart()
      initWorkOrderAuditChart()
    }
  } catch (error) {
    console.error("获取工单效率统计总数据失败:", error)
    ElMessage.error("获取工单效率统计总数据失败")
  }
}

// 手动触发图表resize
const resizeCharts = () => {
  if (window.chartInstances) {
    if (window.chartInstances.workOrderRateChart) {
      window.chartInstances.workOrderRateChart.resize();
    }
    if (window.chartInstances.workOrderAuditChart) {
      window.chartInstances.workOrderAuditChart.resize();
    }
  }
};

// 查询时重新加载图表数据并resize
const queryList = () => {
  pagination.pageNum = 1;
  getList();
  updateChartsData();
  
  // 延迟执行resize，确保DOM已更新
  setTimeout(resizeCharts, 100);
};

// 监听表单展开/收起状态变化，重新调整图表大小
watch(() => isFormExpanded.value, () => {
  setTimeout(resizeCharts, 300);
});

onMounted(async () => {
  // 获取工单效率数据列表
  getList()
  
  // 更新图表数据
  updateChartsData()
  
  dictStore.fetchDict([
    'work_order_type',
  ]);
  
  // 初始化图表
  setTimeout(() => {
    initWorkOrderRateChart()
    initWorkOrderAuditChart()
  }, 0)
})
</script>

<style lang="less" scoped>
@import "@/assets/style/_cus_header.less";
@import "@/assets/style/_cus_list.less";

.wrap {
  height: 100%;
  flex: 1;
  overflow: hidden;
  background-color: var(--el-bg-color);
  border-radius: 6px;
  padding: 20px;
  box-sizing: border-box;
}

.cus-container {
  display: flex;
  flex-direction: column;
  gap: 20px;
  height: 100%;
  width: 100%;
}

.cus-wrap {
  display: flex;
  gap: 20px;
  flex: 1;
  overflow: hidden;
}

.cus-header {
  margin-bottom: 0px;
  background-color: var(--el-bg-color);
  padding: 20px;
  border-radius: 6px;

  .form-item-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 12px;
    width: 100%;

    .el-form-item:nth-child(n + 3):not(:last-child) {
      display: none;
    }

    &.is-expanded {
      .el-form-item:nth-child(n + 3):not(:last-child) {
        display: flex;
      }
    }
  }

  .search-buttons {
    display: flex;
    align-items: center;
    justify-content: flex-end;
    grid-column: -2 / -1;
  }

  .el-form-item {
    width: 100%;
    margin-bottom: 0px;

    .el-input,
    .el-select,
    .el-date-editor {
      width: 100%;
    }
  }
}

.cus-main {
  display: flex;
  flex-direction: column;
  flex: 1;
  overflow: auto; 
  height: 100%;

  .cus-list {
		display: flex;
		flex-direction: column;
		flex: 1;
		overflow: hidden;
		position: relative;

		// 表格自动填充剩余空间
		.cus-table {
			flex: 1;
			overflow: auto;
			height: 100%;
		}

		// 分页控件固定在底部
		.cus-pages {
			margin-top: 10px;
		}
	}
}

.cus-message {
  width: 320px;
  min-width: 300px;
  max-width: 400px;
  border-radius: 6px;
  box-sizing: border-box;
  padding: 12px;
  background-color: var(--el-bg-color);
  height: 100%;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  gap: 10px;

  .chart-container {
    display: flex;
    flex-direction: column;
    height: 100%;
    gap: 20px;

    .chart-item {
      background-color: #fff;
      border-radius: 8px;
      padding: 15px;
      display: flex;
      flex-direction: column;
      flex: 1;
      box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);

      .chart-title {
        font-size: 16px;
        font-weight: 500;
        margin: 0 0 15px 0;
        text-align: center;
      }

      .chart {
        flex: 1;
        height: 240px;
      }

      .chart-summary {
        margin-top: 10px;
        text-align: center;
        color: #606266;
        font-size: 14px;
      }
    }
  }
}

// 进度条样式
.progress-container {
  display: flex;
  align-items: center;
  width: 100%;
  
  .progress-bar {
    flex: 1;
    height: 16px;
    background-color: #f0f0f0;
    border-radius: 8px;
    overflow: hidden;
    margin-right: 10px;
    
    .progress-inner {
      height: 100%;
      border-radius: 8px;
    }
  }
  
  .progress-text {
    min-width: 40px;
    text-align: right;
    font-size: 12px;
  }
}
</style>
