<template>
  <el-drawer :title="formData.id ? '编辑消息' : '新增消息'" v-model="dialogVisible" size="650px" :close-on-click-modal="false"
    :before-close="handleClose" direction="rtl">
    <el-form :model="formData" :rules="formRules" class="drawer-form" ref="messageFormRef" label-width="120px">
      <el-form-item label="标题" prop="subject">
        <el-input v-model="formData.subject" placeholder="请输入主题" />
      </el-form-item>

      <el-form-item label="发送对象" prop="roleCodes">
        <el-select v-model="formData.roleCodes" multiple filterable placeholder="请选择角色（可多选）" style="width: 100%">
          <el-option v-for="role in roleOptions" :key="role.value" :label="role.label" :value="role.value" />
        </el-select>
      </el-form-item>

      <el-form-item v-if="isShowSubCenter" label="分中心" prop="subCenterCodes">
        <el-select v-model="formData.subCenterCodes" multiple placeholder="请选择分中心（可多选）" style="width: 100%">
          <el-option v-for="center in subCenterOptions" :key="center.value" :label="center.label"
            :value="center.value" />
        </el-select>
      </el-form-item>

      <el-form-item label="立即发送" prop="sendImmediately">
        <el-switch v-model="formData.sendImmediately" />
      </el-form-item>

      <el-form-item v-if="!formData.sendImmediately" label="发送时间" prop="sendTime">
        <el-date-picker v-model="formData.sendTime" type="datetime" placeholder="选择发送时间"
          value-format="YYYY-MM-DD HH:mm:ss" style="width: 100%" :disabled-date="disabledDate" />
      </el-form-item>
      <el-form-item label="内容" prop="content">
        <QuillEditor ref="editor" v-model="formData.content" />
      </el-form-item>
    </el-form>
    <div class="drawer-footer">
      <el-button @click="handleClose" :disabled="isSubmitting">
        取 消
      </el-button>
      <el-button @click="saveDraft" :loading="loading">
        保存草稿
      </el-button>
      <el-button type="primary" @click="submitForm" :loading="isSubmitting">
        发送
      </el-button>
    </div>
  </el-drawer>
</template>

<script setup>
import { ref, reactive, watch, computed } from "vue"
import { ElMessage, ElMessageBox } from "element-plus"
import maintainanceAPI from "@/api/maintainance"
import { validateNoWhitespace } from "@/utils/validateRule"
import { useDictStore } from "@/stores/modules/dict"
import QuillEditor from "@/components/editor/quill.vue"
import _D from "@/edata/_osp_data"

const subCenterOptions = _D.subCenterList

const props = defineProps({
  visible: {
    type: Boolean,
    default: false,
  },
  data: {
    type: Object,
    default: () => ({}),
  },
})

const emit = defineEmits(["update:visible", "success"])
const editor = ref(null)
const dialogVisible = ref(false)
const messageFormRef = ref(null)
const dictStore = useDictStore()
const isSubmitting = ref(false)
const loading = ref(false)
const isShowSubCenter = computed(() => {
  return !(
    (formData.roleCodes.includes("head_office") &&
      formData.roleCodes.length === 1) ||
    formData.roleCodes.length === 0
  )
})

const roleOptions = computed(() => dictStore.getDictByType("user_role"))

const formData = reactive({
  id: "",
  subject: "",
  messageType: "SYSTEM",
  content: "",
  roleCodes: [],
  subCenterCodes: [],
  sendImmediately: false,
  sendTime: null,
})

const disabledDate = (time) => {
  return time.getTime() < Date.now() - 8.64e7 // 减去一天的时间（毫秒数），确保能选择当天
}

// 表单验证规则
const formRules = reactive({
  subject: [
    { required: true, message: "请输入标题", trigger: "blur" },
    { max: 100, message: "标题不能超过100个字符", trigger: "blur" },
    { validator: validateNoWhitespace, trigger: "blur" },
  ],
  roleCodes: [{ required: true, message: "请选择发送对象", trigger: "blur" }],
  content: [{ required: true, message: "请输入消息内容", trigger: "blur" }],
  sendTime: [{ required: true, message: "请选择发送时间", trigger: "blur" }],
})

watch(
  () => props.visible,
  (val) => {
    dialogVisible.value = val
    if (val && props.data.id) {
      // 编辑模式，获取详细信息
      getMessageDetail(props.data.id)
    } else if (val) {
      // 新增模式，重置表单
      resetForm()
    }
  },
  { immediate: true }
)

// 获取消息详情
const getMessageDetail = (id) => {
  loading.value = true
  maintainanceAPI
    .getMessageDetail({ id })
    .then((res) => {
      loading.value = false
      if (res.success && res.result) {
        Object.assign(formData, res.result)
      } else {
        ElMessage.error(res.error || "获取消息详情失败")
      }
    })
    .catch((error) => {
      loading.value = false
      console.error("获取消息详情失败:", error)
      ElMessage.error("获取消息详情失败")
    })
}

// 提交表单
const submitForm = async () => {
  if (isSubmitting.value) return
  if (!messageFormRef.value) return

  try {
    await messageFormRef.value.validate()

    const submitData = { ...formData }

    // 校验发送时间不能早于当前时间
    if (!submitData.sendImmediately && submitData.sendTime && new Date(submitData.sendTime).getTime() < Date.now()) {
      ElMessage.warning("发送时间不能早于当前时间")
      return
    }

    if (submitData.sendImmediately) {
      try {
        await ElMessageBox.confirm(
          "确定要立即发送此消息吗？",
          "确认发送",
          {
            confirmButtonText: "确定",
            cancelButtonText: "取消",
            type: "warning",
          }
        )
      } catch (error) {
        // 用户点击了取消或关闭弹窗
        return
      }
    }

    isSubmitting.value = true

    // 如果选择立即发送但没有设置发送时间，使用当前时间
    if (submitData.sendImmediately) {
      submitData.sendTime = undefined
    }
    
    const apiMethod = formData.id
      ? maintainanceAPI.updateAndSendMessage
      : maintainanceAPI.createAndSendMessage

    const res = await apiMethod({ ...submitData })

    if (res.data && res.data.success) {
      ElMessage.success("操作成功")
      emit("success")
      handleClose()
    } else {
      ElMessage.error(res.data.error || "操作失败")
    }
  } catch (error) {
    console.error("表单验证失败:", error)
    ElMessage.warning("请完善表单信息")
  } finally {
    isSubmitting.value = false
  }
}

// 保存草稿
const saveDraft = async () => {
  if (isSubmitting.value) return
  if (!messageFormRef.value) return

  try {
    loading.value = true

    const submitData = { ...formData }

    // 如果选择立即发送但没有设置发送时间，使用当前时间
    if (submitData.sendImmediately && !submitData.sendTime) {
      const now = new Date()
      submitData.sendTime = now
        .toISOString()
        .split(".")[0]
        .replace("T", " ")
    }

    const apiMethod = formData.id
      ? maintainanceAPI.updateMessage
      : maintainanceAPI.createMessage

    const res = await apiMethod(submitData)

    if (res.data && res.data.success) {
      ElMessage.success("操作成功")
      emit("success")
      handleClose()
    } else {
      ElMessage.error(res.data.error || "操作失败")
    }
  } catch (error) {
    console.error("表单验证失败:", error)
    ElMessage.warning("请完善表单信息")
  } finally {
    loading.value = false
  }
}

// 重置表单
const resetForm = () => {
  Object.assign(formData, {
    id: "",
    subject: "",
    messageType: "SYSTEM",
    content: "",
    recipientUserIds: [],
    roleCodes: [],
    subCenterCodes: [],
    sendImmediately: false,
    sendTime: null,
  })

  if(editor.value?.clearContent) {
    editor.value?.clearContent()
  }

  if (messageFormRef.value) {
    messageFormRef.value.resetFields()
  }
}

// 关闭对话框
const handleClose = () => {
  emit("update:visible", false)
  resetForm()
}
</script>

<style lang="less" scoped>
.drawer-form {
  height: calc(100% - 80px);
  overflow-y: auto;
  padding-right: 12px;
}

.drawer-footer {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  padding: 16px 20px;
  background-color: #fff;
  border-top: 1px solid #e4e7ed;
  text-align: right;
  

  .el-button {
    margin-left: 8px;
  }
}
</style>
