<template>
	<div class="wrap">
		<div class="cus-header">
			<el-form :model="searchForm" label-width="80px">
				<!-- 基础查询条件 -->
				<div class="form-item-grid" :class="[isExpanded ? 'is-expanded' : '']">
					<el-form-item label="标题">
						<el-input v-model="searchForm.subject" placeholder="输入标题" clearable />
					</el-form-item>

					<el-form-item label="发送对象">
						<el-select v-model="searchForm.roleCode" placeholder="选择发送对象" style="width: 100%">
							<el-option v-for="role in roleOptions" :key="role.value" :label="role.label" :value="role.value" />
						</el-select>
					</el-form-item>

					<!-- 展开的查询条件 -->
					<el-form-item label="分中心">
						<el-select v-model="searchForm.subCenterCode" placeholder="选择分中心" style="width: 100%">
							<el-option v-for="role in subCenterOptions" :key="role.value" :label="role.label" :value="role.value" />
						</el-select>
					</el-form-item>
					<el-form-item label="发送时间">
						<el-date-picker
							v-model="searchForm.sendTimeRange"
							type="daterange"
							range-separator="至"
							start-placeholder="开始日期"
							end-placeholder="结束日期"
							value-format="YYYY-MM-DD"
							style="width: 100%;"
						/>
					</el-form-item>

					<el-form-item label="状态">
						<el-select v-model="searchForm.status" placeholder="选择状态" clearable style="width: 100%;">
							<el-option label="草稿" value="DRAFT" />
							<el-option label="已发送" value="SENT" />
							<el-option label="发送失败" value="FAILED" />
							<el-option label="待发送" value="PENDING" />
						</el-select>
					</el-form-item>
					<el-form-item label="创建人">
            <el-input v-model="searchForm.createdByName" clearable placeholder="输入创建人" />
          </el-form-item>
					<!-- 查询按钮组 -->
					<div class="search-buttons">
						<el-button type="default" @click="onReset">重置</el-button>
						<el-button type="primary" @click="queryList">查询</el-button>
						<el-link type="primary" @click="toggleExpand" :underline="false" style="margin-left: 10px;">
							{{ isExpanded ? '收起' : '展开' }}
							<el-icon>
								<arrow-up v-if="isExpanded" />
								<arrow-down v-else />
							</el-icon>
						</el-link>
					</div>
				</div>
			</el-form>
		</div>
		<div class="cus-main" ref="mainRef">
			<div class="cus-list" v-loading="loading" ref="cusListRef">
				<div style="text-align: right;">
					<el-button type="success" plain @click="handleAdd">新增消息</el-button>
				</div>
				<el-table :data="listArr" class="cus-table">
					<el-table-column fixed align="center" type="index" label="序号" width="60" />
					<el-table-column fixed align="center" prop="subject" label="标题" minWidth="150">
						<template #default="scope">
							{{ scope.row.subject || '-' }}
						</template>
					</el-table-column>
					<el-table-column align="center" prop="messageType" label="消息类型" width="100">
						<template #default="scope">
							{{ getDictLabel('message_type', scope.row.messageType) }}
						</template>
					</el-table-column>
					<el-table-column align="center" prop="roleCodes" label="发送对象" width="100">
						<template #default="scope">
							{{ getRoldNames(scope.row.roleCodes) }}
						</template>
					</el-table-column>
					<el-table-column align="center" prop="subCenterCodes" label="分中心" width="160" show-overflow-tooltip>
						<template #default="scope">
							{{ getSubCenterNames(scope.row.subCenterCodes) }}
						</template>
					</el-table-column>
					<el-table-column align="center" prop="status" label="状态" width="100">
						<template #default="scope">
							<el-tag :type="getStatusType(scope.row.status)">{{ getDictLabel('message_status', scope.row.status) }}</el-tag>
						</template>
					</el-table-column>
					<el-table-column align="center" prop="sendTime" label="发送时间" width="180">
						<template #default="scope">
							{{ scope.row.sendTime && scope.row.sendTime.replace('T', ' ') || '-' }}
						</template>
					</el-table-column>
					<el-table-column align="center" prop="createdByName" label="创建人" width="120">
						<template #default="scope">
							{{ scope.row.createdByName || '-' }}
						</template>
					</el-table-column>
					<el-table-column align="center" prop="createdAt" label="创建时间" width="180">
						<template #default="scope">
							{{ scope.row.createdAt && scope.row.createdAt.replace('T', ' ') || '-' }}
						</template>
					</el-table-column>
					<el-table-column fixed="right" align="center" label="操作" width="120">
						<template #default="scope">
							<el-button link type="primary" @click="handleEdit(scope.row)" v-if="scope.row.status === 'DRAFT'">编辑</el-button>
							<el-button link type="primary" @click="handleView(scope.row)" v-if="scope.row.status !== 'DRAFT'">查看</el-button>
							<el-button link type="danger" @click="handleDelete(scope.row)" v-if="scope.row.status === 'DRAFT'">删除</el-button>
						</template>
					</el-table-column>
				</el-table>
				<el-pagination class="cus-pages" v-if="total" background layout="sizes, prev, pager, next, ->, total"
					:page-sizes="[10, 20, 30]" :page-size="pagination.pageSize" :current-page="pagination.pageNum" :total="total"
					@size-change="changeSize" @current-change="changeCurrent" />
			</div>
		</div>
		<Operate v-model:visible="operateVisible" :data="operateData" @success="handleOperateSuccess" />
		<View v-model:visible="viewVisible" :data="viewData" />
	</div>
</template>

<script setup>
import { ref, reactive, onMounted, computed } from 'vue';
import { useTablePagination } from '@/composables/useTablePagination';
import API from '@/api/maintainance';
import _ from 'lodash';
import _D from '@/edata/_osp_data';
import { ArrowDown, ArrowUp } from '@element-plus/icons-vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import Operate from './components/Operate.vue';
import View from './components/View.vue';
import { useDictStore } from '@/stores/modules/dict';

const dictStore = useDictStore();
const roleOptions = computed(() => dictStore.getDictByType('user_role'));
const isExpanded = ref(false);
const searchForm = reactive({
	subject: '',
	messageType: '',
	status: '',
	sendTimeRange: [],
	createdBy: '',
	roleCode: null,
	subCenterCode: null
});

const {
	loading,
	listArr,
	total,
	pagination,
	getList,
	queryList,
	changeSize,
	changeCurrent
} = useTablePagination(
	API.getAllMessagePage,
	() => {
		const params = { ...searchForm };
		if (params.sendTimeRange && params.sendTimeRange.length === 2) {
			params.startTime = params.sendTimeRange[0];
			params.endTime = params.sendTimeRange[1];
			delete params.sendTimeRange;
		}
		return params;
	},
	{ manual: true }
);

const subCenterOptions = _D.subCenterList;
const operateVisible = ref(false);
const operateData = ref({});
const viewVisible = ref(false);
const viewData = ref({});
const subCenterMap = subCenterOptions.reduce((acc, cur) => {
  acc[cur.value] = cur.label;
  return acc;
}, {});

const getDictLabel = (dictType, value) => {
  const dictMap = dictStore.getDictMapByType(dictType)
  return dictMap[value] || "-"
}

const getRoldNames = (value) => {
	const dictMap = dictStore.getDictMapByType('user_role')
  return value?.map(item => dictMap[item])?.join(',') || '-'
}

const getSubCenterNames = (value) => {
  return value?.map(item => subCenterMap[item])?.join(',') || '-'
}

const getStatusType = (status) => {
	const typeMap = {
		'DRAFT': 'info',
		'SENT': 'success',
		'FAILED': 'danger',
		'TO_SEND': 'warning'
	};
	return typeMap[status] || '';
};

const onReset = () => {
	Object.assign(searchForm, {
		subject: '',
		messageType: '',
		status: '',
		sendTimeRange: [],
		createdBy: '',
		roleCode: null,
		subCenterCode: null
	});
	queryList();
};

const toggleExpand = () => {
	isExpanded.value = !isExpanded.value;
};

const handleAdd = () => {
	operateData.value = {};
	operateVisible.value = true;
};

const handleView = (row) => {
	viewData.value = { ...row };
	viewVisible.value = true;
};

const handleEdit = (row) => {
	operateData.value = { ...row };
	operateVisible.value = true;
};

const handleOperateSuccess = () => {
	queryList();
};

const handleDelete = (row) => {
	ElMessageBox.confirm('确认要删除该消息吗？', '提示', {
		confirmButtonText: '确定',
		cancelButtonText: '取消',
		type: 'warning'
	}).then(() => {
		API.deleteMessage({ id: row.id }).then(res => {
			if (res.success) {
				ElMessage.success('删除成功');
				queryList();
			} else {
				ElMessage.error(res.error || '删除失败');
			}
		}).catch(error => {
			ElMessage.error(error.message || '请求失败');
		});
	});
};

onMounted(() => {
	getList();
	dictStore.fetchDict(['user_role', 'message_type', 'message_status']);
});
</script>

<style lang="less" scoped>
@import '@/assets/style/_cus_header.less';
@import '@/assets/style/_cus_list.less';

.wrap {
	height: 100%;
}

.cus-header {
	margin-bottom: 0px;

	.form-item-grid {
		display: grid;
		grid-template-columns: repeat(3, 1fr);
		gap: 12px;
		width: 100%;

		/* 默认状态（收起）- 只显示前两个元素和按钮组 */
		.el-form-item:nth-child(n+3):not(:last-child) {
			display: none;
		}

		/* 展开状态 - 显示所有元素 */
		&.is-expanded {
			.el-form-item:nth-child(n+3):not(:last-child) {
				display: flex;
			}
		}
	}

	.search-buttons {
		display: flex;
		align-items: center;
		justify-content: flex-end;
		grid-column: -2 / -1;
	}

	.el-form-item {
		width: 100%;
		margin-bottom: 0px;

		.el-input,
		.el-select,
		.el-date-editor {
			width: 100%;
		}
	}
}

.cus-main {
	display: flex;
	flex-direction: column;
	flex: 1;
	overflow: hidden;

	>.el-button {
		margin-bottom: 10px;
	}

	.cus-list {
		display: flex;
		flex-direction: column;
		flex: 1;
		overflow: hidden;
		position: relative;

		// 表格自动填充剩余空间
		.cus-table {
			flex: 1;
			overflow: auto;
			height: 100%;
		}

		// 分页控件固定在底部
		.cus-pages {
			margin-top: 10px;
		}
	}
}
</style>
