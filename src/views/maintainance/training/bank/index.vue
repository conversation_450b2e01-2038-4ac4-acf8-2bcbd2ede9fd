<template>
  <div class="wrap">
    <div class="cus-header">
      <el-form :model="searchForm" label-width="80px">
        <!-- 基础查询条件 -->
        <div class="form-item-grid" :class="[isExpanded ? 'is-expanded' : '']">
          <el-form-item label="题库名称">
            <el-input v-model="searchForm.name" placeholder="输入题库名称" clearable />
          </el-form-item>

          <el-form-item label="分类">
            <el-select v-model="searchForm.categoryId" placeholder="选择分类" clearable style="width: 100%;">
              <el-option v-for="item in categoryOptions" :key="item.value" :label="item.label" :value="item.value" />
            </el-select>
          </el-form-item>

          <!-- 展开的查询条件 -->
          <el-form-item label="创建人">
            <el-input v-model="searchForm.createdBy" placeholder="输入创建人" clearable />
          </el-form-item>

          <!-- 查询按钮组 -->
          <div class="search-buttons">
            <el-button type="default" @click="onReset">重置</el-button>
            <el-button type="primary" @click="queryList">查询</el-button>
            <el-link type="primary" @click="toggleExpand" :underline="false" style="margin-left: 10px;">
              {{ isExpanded ? '收起' : '展开' }}
              <el-icon>
                <arrow-up v-if="isExpanded" />
                <arrow-down v-else />
              </el-icon>
            </el-link>
          </div>
        </div>
      </el-form>
    </div>
    
    <div class="cus-main" ref="mainRef">
      <div class="cus-list" v-loading="loading" ref="cusListRef">
        <div style="text-align: right;">
          <el-button type="success" plain @click="handleAdd">新增题库</el-button>
        </div>
        
        <el-table :data="listArr" class="cus-table">
          <el-table-column fixed align="center" type="index" label="序号" width="60" />
          <el-table-column fixed align="center" prop="name" label="题库名称" min-width="200">
            <template #default="scope">
              {{ scope.row.name || '-' }}
            </template>
          </el-table-column>
          <el-table-column align="center" prop="categoryName" label="分类名称" width="150">
            <template #default="scope">
              {{ scope.row.categoryName || '-' }}
            </template>
          </el-table-column>
          <el-table-column align="center" prop="singleChoiceCount" label="单选题数量" width="120">
            <template #default="scope">
              {{ scope.row.singleChoiceCount || 0 }}
            </template>
          </el-table-column>
          <el-table-column align="center" prop="multipleChoiceCount" label="多选题数量" width="120">
            <template #default="scope">
              {{ scope.row.multipleChoiceCount || 0 }}
            </template>
          </el-table-column>
          <el-table-column align="center" prop="totalCount" label="总题数" width="100">
            <template #default="scope">
              {{ scope.row.totalCount || 0 }}
            </template>
          </el-table-column>
          <el-table-column align="center" prop="createdBy" label="创建人" width="120">
            <template #default="scope">
              {{ scope.row.createdBy || '-' }}
            </template>
          </el-table-column>
          <el-table-column align="center" prop="createdAt" label="创建时间" width="180">
            <template #default="scope">
              {{ scope.row.createdAt && scope.row.createdAt.replace('T', ' ') || '-' }}
            </template>
          </el-table-column>
          <el-table-column align="center" prop="updatedBy" label="修改人" width="120">
            <template #default="scope">
              {{ scope.row.updatedBy || '-' }}
            </template>
          </el-table-column>
          <el-table-column align="center" prop="updatedAt" label="修改时间" width="180">
            <template #default="scope">
              {{ scope.row.updatedAt && scope.row.updatedAt.replace('T', ' ') || '-' }}
            </template>
          </el-table-column>
          <el-table-column fixed="right" align="center" label="操作" width="160">
            <template #default="scope">
              <el-button link type="primary" @click="handleEdit(scope.row)">编辑</el-button>
              <el-button link type="danger" @click="handleDelete(scope.row)">删除</el-button>
            </template>
          </el-table-column>
        </el-table>
        
        <el-pagination class="cus-pages" v-if="total" background layout="sizes, prev, pager, next, ->, total"
          :page-sizes="[10, 20, 30]" :page-size="pagination.pageSize" :current-page="pagination.pageNum" :total="total"
          @size-change="changeSize" @current-change="changeCurrent" />
      </div>
    </div>
    
    <Operate v-model:visible="operateVisible" :data="operateData" @success="handleOperateSuccess" />
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, computed } from 'vue';
import { useTablePagination } from '@/composables/useTablePagination';
import API from '@/api/maintainance';
import { ArrowDown, ArrowUp } from '@element-plus/icons-vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import Operate from './components/Operate.vue';

// 展开收缩状态
const isExpanded = ref(false);

// 搜索表单
const searchForm = reactive({
  name: '',
  categoryId: '',
  createdBy: ''
});

// 使用组合式函数管理表格和分页状态
const {
  loading,
  listArr,
  total,
  pagination,
  getList,
  queryList,
  changeSize,
  changeCurrent,
} = useTablePagination(
  API.getQuestionBankPage,
  () => searchForm,
  { manual: true }
);

// 操作弹窗相关
const operateVisible = ref(false);
const operateData = ref({});

// 分类选项
const categoryOptions = ref([]);

// 获取分类列表
const getCategoryList = async () => {
  try {
    const res = await API.getQuestionCategoryList();
    if (res.success && res.result) {
      categoryOptions.value = res.result.map(item => ({
        label: item.name,
        value: item.id
      }));
    }
  } catch (error) {
    console.error('获取分类列表失败:', error);
  }
};

// 重置搜索表单
const onReset = () => {
  searchForm.name = '';
  searchForm.categoryId = '';
  searchForm.createdBy = '';
  queryList();
};

// 切换展开收缩
const toggleExpand = () => {
  isExpanded.value = !isExpanded.value;
};

// 新增题库
const handleAdd = () => {
  operateData.value = {};
  operateVisible.value = true;
};

// 编辑题库
const handleEdit = (row) => {
  operateData.value = { ...row };
  operateVisible.value = true;
};

// 删除题库
const handleDelete = (row) => {
  ElMessageBox.confirm('确认要删除该题库吗？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning',
  }).then(() => {
    API.deleteQuestionBank({ id: row.id }).then(response => {
      if (response.data.success) {
        ElMessage.success('删除成功');
        getList();
      } else {
        ElMessage.error(response.data.error || '删除失败');
      }
    }).catch(error => {
      ElMessage.error(error.message || '请求失败');
    });
  }).catch(() => {
    // 用户取消删除
  });
};

// 操作成功回调
const handleOperateSuccess = () => {
  getList();
};

// 页面初始化
onMounted(() => {
  getCategoryList();
  getList();
});
</script>

<style lang="less" scoped>
@import '@/assets/style/_cus_header.less';
@import '@/assets/style/_cus_list.less';

.wrap {
  height: 100%;
}

.cus-header {
  margin-bottom: 0px;

  .form-item-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    /* 每行3个查询条件 */
    gap: 12px;
    width: 100%;

    /* 默认状态（收起）- 根据实际需要调整显示的查询条件数量 */
    .el-form-item:nth-child(n+3):not(:last-child) {
      /* 假设默认显示前2个 */
      display: none;
    }

    /* 展开状态 - 显示所有元素 */
    &.is-expanded {
      .el-form-item:nth-child(n+3):not(:last-child) {
        display: flex;
      }
    }
  }

  .search-buttons {
    display: flex;
    align-items: center;
    justify-content: flex-end;
    grid-column: -2 / -1;
    /* 确保按钮组在最后一列 */
  }

  .el-form-item {
    width: 100%;
    margin-bottom: 0px;

    .el-input,
    .el-select,
    .el-date-editor {
      width: 100%;
    }
  }
}

.cus-main {
  display: flex;
  flex-direction: column;
  flex: 1;
  overflow: hidden;

  >.el-button {
    margin-bottom: 10px;
  }

  .cus-list {
    display: flex;
    flex-direction: column;
    flex: 1;
    overflow: hidden;
    position: relative;

    // 表格自动填充剩余空间
    .cus-table {
      flex: 1;
      overflow: auto;
      height: 100%;
    }

    // 分页控件固定在底部
    .cus-pages {
      margin-top: 10px;
    }
  }
}
</style>
