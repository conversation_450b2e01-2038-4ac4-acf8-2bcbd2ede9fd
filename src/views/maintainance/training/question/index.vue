<template>
  <div class="wrap">
    <div class="cus-header">
      <el-form :model="searchForm" label-width="80px">
        <!-- 基础查询条件 -->
        <div class="form-item-grid" :class="[isExpanded ? 'is-expanded' : '']">
          <el-form-item label="题目标题">
            <el-input v-model="searchForm.title" placeholder="输入题目标题" clearable />
          </el-form-item>

          <el-form-item label="分类">
            <el-select v-model="searchForm.categoryId" placeholder="选择分类" clearable style="width: 100%;" @change="handleCategoryChange">
              <el-option v-for="item in categoryOptions" :key="item.value" :label="item.label" :value="item.value" />
            </el-select>
          </el-form-item>

          <!-- 展开的查询条件 -->
          <el-form-item label="题库">
            <el-select v-model="searchForm.bankId" placeholder="选择题库" clearable style="width: 100%;">
              <el-option v-for="item in bankOptions" :key="item.value" :label="item.label" :value="item.value" />
            </el-select>
          </el-form-item>

          <el-form-item label="题目类型">
            <el-select v-model="searchForm.type" placeholder="选择题目类型" clearable style="width: 100%;">
              <el-option label="单选题" value="SINGLE" />
              <el-option label="多选题" value="MULTIPLE" />
            </el-select>
          </el-form-item>

          <el-form-item label="难度">
            <el-select v-model="searchForm.difficulty" placeholder="选择难度" clearable style="width: 100%;">
              <el-option label="简单" :value="1" />
              <el-option label="一般" :value="2" />
              <el-option label="困难" :value="3" />
            </el-select>
          </el-form-item>

          <!-- 查询按钮组 -->
          <div class="search-buttons">
            <el-button type="default" @click="onReset">重置</el-button>
            <el-button type="primary" @click="queryList">查询</el-button>
            <el-link type="primary" @click="toggleExpand" :underline="false" style="margin-left: 10px;">
              {{ isExpanded ? '收起' : '展开' }}
              <el-icon>
                <arrow-up v-if="isExpanded" />
                <arrow-down v-else />
              </el-icon>
            </el-link>
          </div>
        </div>
      </el-form>
    </div>
    
    <div class="cus-main" ref="mainRef">
      <div class="cus-list" v-loading="loading" ref="cusListRef">
        <div style="text-align: right;">
          <el-button type="success" plain @click="handleAdd">新增题目</el-button>
        </div>
        
        <el-table :data="listArr" class="cus-table">
          <el-table-column fixed align="center" type="index" label="序号" width="60" />
          <el-table-column fixed align="center" prop="title" label="题目标题" min-width="250">
            <template #default="scope">
              {{ scope.row.title || '-' }}
            </template>
          </el-table-column>
          <el-table-column align="center" prop="bankName" label="题库名称" width="150">
            <template #default="scope">
              {{ scope.row.bankName || '-' }}
            </template>
          </el-table-column>
          <el-table-column align="center" prop="type" label="题目类型" width="100">
            <template #default="scope">
              {{ getTypeLabel(scope.row.type) }}
            </template>
          </el-table-column>
          <el-table-column align="center" prop="difficulty" label="难度" width="80">
            <template #default="scope">
              {{ getDifficultyLabel(scope.row.difficulty) }}
            </template>
          </el-table-column>
          <!-- <el-table-column align="center" prop="score" label="分值" width="80">
            <template #default="scope">
              {{ scope.row.score || 0 }}
            </template>
          </el-table-column> -->
          <el-table-column align="center" prop="createdBy" label="创建人" width="120">
            <template #default="scope">
              {{ scope.row.createdBy || '-' }}
            </template>
          </el-table-column>
          <el-table-column align="center" prop="createdAt" label="创建时间" width="180">
            <template #default="scope">
              {{ scope.row.createdAt && scope.row.createdAt.replace('T', ' ') || '-' }}
            </template>
          </el-table-column>
          <el-table-column align="center" prop="updatedBy" label="修改人" width="120">
            <template #default="scope">
              {{ scope.row.updatedBy || '-' }}
            </template>
          </el-table-column>
          <el-table-column align="center" prop="updatedAt" label="修改时间" width="180">
            <template #default="scope">
              {{ scope.row.updatedAt && scope.row.updatedAt.replace('T', ' ') || '-' }}
            </template>
          </el-table-column>
          <el-table-column fixed="right" align="center" label="操作" width="160">
            <template #default="scope">
              <el-button link type="primary" @click="handleEdit(scope.row)">编辑</el-button>
              <el-button link type="danger" @click="handleDelete(scope.row)">删除</el-button>
            </template>
          </el-table-column>
        </el-table>
        
        <el-pagination class="cus-pages" v-if="total" background layout="sizes, prev, pager, next, ->, total"
          :page-sizes="[10, 20, 30]" :page-size="pagination.pageSize" :current-page="pagination.pageNum" :total="total"
          @size-change="changeSize" @current-change="changeCurrent" />
      </div>
    </div>
    
    <Operate v-model:visible="operateVisible" :data="operateData" :category-options="categoryOptions" @success="handleOperateSuccess" />
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue';
import { useTablePagination } from '@/composables/useTablePagination';
import API from '@/api/maintainance';
import { ArrowDown, ArrowUp } from '@element-plus/icons-vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import Operate from './components/Operate.vue';

// 展开收缩状态
const isExpanded = ref(false);

// 搜索表单
const searchForm = reactive({
  title: '',
  categoryId: '',
  bankId: '',
  type: '',
  difficulty: ''
});

// 使用组合式函数管理表格和分页状态
const {
  loading,
  listArr,
  total,
  pagination,
  getList,
  queryList,
  changeSize,
  changeCurrent,
} = useTablePagination(
  API.getQuestionPage,
  () => searchForm,
  { manual: true }
);

// 操作弹窗相关
const operateVisible = ref(false);
const operateData = ref({});

// 分类选项
const categoryOptions = ref([]);
// 题库选项
const bankOptions = ref([]);

// 获取分类列表
const getCategoryList = async () => {
  try {
    const res = await API.getQuestionCategoryList();
    if (res.success && res.result) {
      categoryOptions.value = res.result.map(item => ({
        label: item.name,
        value: item.id
      }));
    }
  } catch (error) {
    console.error('获取分类列表失败:', error);
  }
};

// 获取题库列表
const getBankList = async (categoryId = null) => {
  try {
    const params = categoryId ? { categoryId } : {};
    const res = await API.getQuestionBankListByCategory(params);
    if (res.success && res.result) {
      bankOptions.value = res.result.map(item => ({
        label: item.name,
        value: item.id
      }));
    }
  } catch (error) {
    console.error('获取题库列表失败:', error);
  }
};

// 分类变化处理
const handleCategoryChange = (categoryId) => {
  // 清空题库选择
  searchForm.bankId = '';
  // 重新获取题库列表
  if (categoryId) {
    getBankList(categoryId);
  } else {
    bankOptions.value = [];
  }
};

// 获取题目类型标签
const getTypeLabel = (type) => {
  const typeMap = {
    'SINGLE': '单选题',
    'MULTIPLE': '多选题'
  };
  return typeMap[type] || type;
};

// 获取难度标签
const getDifficultyLabel = (difficulty) => {
  const difficultyMap = {
    1: '简单',
    2: '一般',
    3: '困难'
  };
  return difficultyMap[difficulty] || difficulty;
};

// 重置搜索表单
const onReset = () => {
  searchForm.title = '';
  searchForm.categoryId = '';
  searchForm.bankId = '';
  searchForm.type = '';
  searchForm.difficulty = '';
  // 清空题库选项
  bankOptions.value = [];
  queryList();
};

// 切换展开收缩
const toggleExpand = () => {
  isExpanded.value = !isExpanded.value;
};

// 新增题目
const handleAdd = () => {
  operateData.value = {};
  operateVisible.value = true;
};

// 编辑题目
const handleEdit = (row) => {
  operateData.value = { ...row };
  operateVisible.value = true;
};

// 删除题目
const handleDelete = (row) => {
  ElMessageBox.confirm('确认要删除该题目吗？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning',
  }).then(() => {
    API.deleteQuestion({ id: row.id }).then(response => {
      if (response.data.success) {
        ElMessage.success('删除成功');
        getList();
      } else {
        ElMessage.error(response.data.error || '删除失败');
      }
    }).catch(error => {
      ElMessage.error(error.message || '请求失败');
    });
  }).catch(() => {
    // 用户取消删除
  });
};

// 操作成功回调
const handleOperateSuccess = () => {
  getList();
};

// 页面初始化
onMounted(() => {
  getCategoryList();
  getList();
});
</script>

<style lang="less" scoped>
@import '@/assets/style/_cus_header.less';
@import '@/assets/style/_cus_list.less';

.wrap {
  height: 100%;
}

.cus-header {
  margin-bottom: 0px;

  .form-item-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    /* 每行3个查询条件 */
    gap: 12px;
    width: 100%;

    /* 默认状态（收起）- 根据实际需要调整显示的查询条件数量 */
    .el-form-item:nth-child(n+3):not(:last-child) {
      /* 假设默认显示前2个 */
      display: none;
    }

    /* 展开状态 - 显示所有元素 */
    &.is-expanded {
      .el-form-item:nth-child(n+3):not(:last-child) {
        display: flex;
      }
    }
  }

  .search-buttons {
    display: flex;
    align-items: center;
    justify-content: flex-end;
    grid-column: -2 / -1;
    /* 确保按钮组在最后一列 */
  }

  .el-form-item {
    width: 100%;
    margin-bottom: 0px;

    .el-input,
    .el-select,
    .el-date-editor {
      width: 100%;
    }
  }
}

.cus-main {
  display: flex;
  flex-direction: column;
  flex: 1;
  overflow: hidden;

  >.el-button {
    margin-bottom: 10px;
  }

  .cus-list {
    display: flex;
    flex-direction: column;
    flex: 1;
    overflow: hidden;
    position: relative;

    // 表格自动填充剩余空间
    .cus-table {
      flex: 1;
      overflow: auto;
      height: 100%;
    }

    // 分页控件固定在底部
    .cus-pages {
      margin-top: 10px;
    }
  }
}
</style>
