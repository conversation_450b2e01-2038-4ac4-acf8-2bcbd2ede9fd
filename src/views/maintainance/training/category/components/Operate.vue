<template>
  <el-dialog
    v-model="dialogVisible"
    :title="form.id ? '编辑分类' : '新增分类'"
    width="500px"
    :close-on-click-modal="false"
    :before-close="handleClose"
  >
    <el-form ref="formRef" :model="form" :rules="rules" label-width="100px">
      <el-form-item label="分类名称" prop="name">
        <el-input
          v-model="form.name"
          placeholder="请输入分类名称"
          maxlength="50"
          show-word-limit
        />
      </el-form-item>
    </el-form>

    <template #footer>
      <el-button @click="handleClose" :disabled="isSubmitting">取 消</el-button>
      <el-button type="primary" @click="submitForm" :loading="isSubmitting">确 定</el-button>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, reactive, watch } from 'vue'
import { ElMessage } from 'element-plus'
import API from '@/api/maintainance'
import { validateNoWhitespace } from '@/utils/validateRule'

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  data: {
    type: Object,
    default: () => ({})
  }
})

const emit = defineEmits(['update:visible', 'success'])

const dialogVisible = ref(false)
const formRef = ref()
const isSubmitting = ref(false)

// 表单数据
const form = reactive({
  id: '',
  name: ''
})

// 表单验证规则
const rules = reactive({
  name: [
    { required: true, message: '请输入分类名称', trigger: 'blur' },
    { max: 50, message: '分类名称不能超过50个字符', trigger: 'blur' },
    { validator: validateNoWhitespace, trigger: 'blur' }
  ]
})

// 监听弹窗显示状态
watch(() => props.visible, (val) => {
  dialogVisible.value = val
  if (val) {
    if (props.data.id) {
      // 编辑模式，填充表单数据
      Object.assign(form, {
        id: props.data.id,
        name: props.data.name || ''
      })
    } else {
      // 新增模式，重置表单
      resetForm()
    }
  }
}, { immediate: true })

// 监听内部弹窗状态变化
watch(dialogVisible, (val) => {
  emit('update:visible', val)
})

// 提交表单
const submitForm = async () => {
  if (isSubmitting.value) return

  try {
    await formRef.value.validate()
    isSubmitting.value = true

    const apiMethod = form.id ? API.editQuestionCategory : API.addQuestionCategory
    const params = {
      name: form.name.trim()
    }

    if (form.id) {
      params.id = form.id
    }

    const res = await apiMethod(params)

    if (res.data.success) {
      ElMessage.success('操作成功')
      emit('success')
      handleClose()
    } else {
      ElMessage.error(res.data.error || '操作失败')
    }
  } catch (error) {
    console.error('表单验证失败:', error)
    ElMessage.warning('请完善表单信息')
  } finally {
    isSubmitting.value = false
  }
}

// 重置表单
const resetForm = () => {
  Object.assign(form, {
    id: '',
    name: ''
  })
}

// 关闭弹窗
const handleClose = () => {
  emit('update:visible', false)
  formRef.value?.resetFields()
  resetForm()
}
</script>

<style lang="less" scoped>
// 可以添加自定义样式
</style>