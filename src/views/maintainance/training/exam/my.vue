<template>
  <div class="wrap">
    <div class="cus-header">
      <el-form :model="searchForm" label-width="80px">
        <div class="form-item-grid" :class="[isExpanded ? 'is-expanded' : '']">
          <el-form-item label="考试名称">
            <el-input v-model="searchForm.name" placeholder="输入考试名称" clearable />
          </el-form-item>

          <el-form-item label="我的状态">
            <el-select v-model="searchForm.userExamStatus" placeholder="选择状态" clearable style="width: 100%;">
              <el-option v-for="item in userExamStatusOptions" :key="item.value" :label="item.label" :value="item.value" />
            </el-select>
          </el-form-item>
          
          <div class="search-buttons">
            <el-button type="default" @click="onReset">重置</el-button>
            <el-button type="primary" @click="queryList">查询</el-button>
            <el-link type="primary" @click="toggleExpand" :underline="false" style="margin-left: 10px;">
              {{ isExpanded ? '收起' : '展开' }}
              <el-icon>
                <arrow-up v-if="isExpanded" />
                <arrow-down v-else />
              </el-icon>
            </el-link>
          </div>
        </div>
      </el-form>
    </div>
    
    <div class="cus-main" ref="mainRef">
      <div class="cus-list" v-loading="loading" ref="cusListRef">
        <el-table :data="listArr" class="cus-table">
          <el-table-column fixed align="center" type="index" label="序号" width="60" />
          <el-table-column align="center" prop="name" label="考试名称" min-width="200" show-overflow-tooltip>
             <template #default="scope">
              {{ scope.row.name || '-' }}
            </template>
          </el-table-column>
          <el-table-column align="center" prop="totalScore" label="总分" width="100" />
          <el-table-column align="center" prop="passScore" label="及格分" width="100" />
          <el-table-column align="center" prop="duration" label="考试时长(分钟)" width="150" />
          <el-table-column align="center" prop="startTime" label="开始时间" width="180">
            <template #default="scope">
              {{ scope.row.startTime && scope.row.startTime.replace('T', ' ') || '-' }}
            </template>
          </el-table-column>
          <el-table-column align="center" prop="endTime" label="结束时间" width="180">
            <template #default="scope">
              {{ scope.row.endTime && scope.row.endTime.replace('T', ' ') || '-' }}
            </template>
          </el-table-column>
          <el-table-column align="center" prop="status" label="考试状态" width="120">
            <template #default="scope">
              <el-tag :type="examStatusTagMap[scope.row.status] || 'info'">
                {{ examStatusMap[scope.row.status] || '-' }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column align="center" prop="userExamStatus" label="我的状态" width="120">
            <template #default="scope">
               <el-tag :type="userExamStatusTagMap[scope.row.userExamStatus] || 'info'">
                {{ userExamStatusMap[scope.row.userExamStatus] || '-' }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column align="center" prop="userScore" label="我的得分" width="100" />
          <el-table-column fixed="right" align="center" label="操作" width="120">
            <template #default="scope">
              <el-button
                v-if="getActionText(scope.row)"
                type="primary"
                link
                @click="handleExamAction(scope.row)"
              >
                {{ getActionText(scope.row) }}
              </el-button>
            </template>
          </el-table-column>
        </el-table>
        
        <el-pagination 
          class="cus-pages" 
          v-if="total" 
          background 
          layout="sizes, prev, pager, next, ->, total"
          :page-sizes="[10, 20, 30]" 
          :page-size="pagination.pageSize" 
          :current-page="pagination.pageNum" 
          :total="total"
          @size-change="changeSize" 
          @current-change="changeCurrent" 
        />
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from "vue";
import { useRouter } from "vue-router";
import { useTablePagination } from '@/composables/useTablePagination';
import API from "@/api/maintainance";
import { cloneDeep } from "lodash-es";
import { ArrowDown, ArrowUp } from '@element-plus/icons-vue';

const router = useRouter();

const mainRef = ref(null);
const cusListRef = ref(null);
const isExpanded = ref(false);

const searchForm = reactive({
  name: "",
  userExamStatus: null,
});

const {
  loading,
  listArr,
  total,
  pagination,
  getList,
  queryList,
  changeSize,
  changeCurrent,
} = useTablePagination(
  API.getCurrentUserExamPage,
  () => cloneDeep(searchForm),
  { manual: true, isPost: true }
);

const userExamStatusOptions = [
  { label: '未开始', value: 'NOT_STARTED' },
  { label: '进行中', value: 'IN_PROGRESS' },
  { label: '已完成', value: 'COMPLETED' },
];

const examStatusMap = {
  DRAFT: '未发布',
  PUBLISHED: '已发布',
  ENDED: '已结束',
};

const examStatusTagMap = {
  DRAFT: 'info',
  PUBLISHED: 'success',
  ENDED: 'warning',
};

const userExamStatusMap = {
  NOT_STARTED: '未开始',
  IN_PROGRESS: '进行中',
  COMPLETED: '已完成',
};

const userExamStatusTagMap = {
  NOT_STARTED: 'info',
  IN_PROGRESS: 'primary',
  COMPLETED: 'success',
};

const toggleExpand = () => {
  isExpanded.value = !isExpanded.value;
};

const onReset = () => {
  searchForm.name = "";
  searchForm.userExamStatus = null;
  queryList();
};

const getActionText = (row) => {
  if (row.userExamStatus === 'COMPLETED') {
    return '查看试卷';
  }
  if (row.status === 'PUBLISHED') {
    if (row.userExamStatus === 'NOT_STARTED') {
      return '开始考试';
    }
    if (row.userExamStatus === 'IN_PROGRESS') {
      return '继续考试';
    }
  }
  return '';
};

const handleExamAction = (row) => {
  const action = getActionText(row);
  if (action === '开始考试' || action === '继续考试') {
    router.push({
      name: 'MaintenanceTrainingExamTaking',
      params: { id: row.id },
    });
  } else if (action === '查看试卷') {
    router.push({
      name: 'MaintenanceTrainingExamResult',
      query: { answerId: row.answerId },
    });
  }
};

onMounted(() => {
  getList();
});
</script>

<style lang="less" scoped>
@import '@/assets/style/_cus_header.less';
@import '@/assets/style/_cus_list.less';

.wrap {
  height: 100%;
}

.cus-header {
  margin-bottom: 0px;

  .form-item-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 12px;
    width: 100%;

    .el-form-item:nth-child(n+3):not(:last-child) {
      display: none;
    }

    &.is-expanded {
      .el-form-item:nth-child(n+3):not(:last-child) {
        display: flex;
      }
    }
  }

  .search-buttons {
    display: flex;
    align-items: center;
    justify-content: flex-end;
    grid-column: -2 / -1;
  }

  .el-form-item {
    width: 100%;
    margin-bottom: 0px;

    .el-input,
    .el-select,
    .el-date-editor {
      width: 100%;
    }
  }
}

.cus-main {
  display: flex;
  flex-direction: column;
  flex: 1;
  overflow: hidden;

  >.el-button {
    margin-bottom: 10px;
  }

  .cus-list {
    display: flex;
    flex-direction: column;
    flex: 1;
    overflow: hidden;
    position: relative;

    .cus-table {
      flex: 1;
      overflow: auto;
      height: 100%;
    }

    .cus-pages {
      margin-top: 10px;
    }
  }
}
</style>
