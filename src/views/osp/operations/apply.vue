<template>
	<div class="wrap">
		<div class="cus-header">
			<el-form :inline="true" :model="formSearch" class="demo-form-inline">
				<div class="cus-branch">
					<el-form-item label="报修单号">
						<el-input class="cus-input-larger" v-model="formSearch.repairOrderSn" placeholder="输入报修单号"
							clearable />
					</el-form-item>
					<el-form-item label="电站编码">
						<el-input class="cus-input" v-model="formSearch.stationCode" placeholder="输入电站编码" clearable />
					</el-form-item>
					<el-form-item label="业主姓名">
						<el-input class="cus-input" v-model="formSearch.linkName" placeholder="输入业主姓名" clearable />
					</el-form-item>
					<el-form-item :label="`${ospName.OSP_NAME}`">
						<el-input class="cus-input" v-model="formSearch.opName" placeholder="输入运维商" clearable />
					</el-form-item>
					<el-form-item label="处理状态">
						<el-select v-model="formSearch.status" placeholder="选择处理状态" clearable>
							<el-option v-for="(item, inx) in repairStatus" :label="item" :value="inx" :key="inx" />
						</el-select>
					</el-form-item>
					<el-form-item label="报修时间">
						<el-date-picker v-model="createTime" type="daterange" value-format="YYYY-MM-DD"
							start-placeholder="开始时间" end-placeholder="结束时间" @change="dateChange" clearable />
					</el-form-item>
				</div>
				<div class="cus-branch right">
					<el-button type="primary" @click="queryList"
						:disabled="!limits.includes('lightoperation:apply:list')">查询</el-button>
					<el-button type="default" @click="onReset">重置</el-button>
					<el-button type="success" plain @click="exportList">导出</el-button>
				</div>
			</el-form>
		</div>
		<div class="cus-main">
			<div class="cus-list" v-loading="loading">
				<el-table :data="listArr" class="cus-table">
					<el-table-column fixed align="center" type="index" label="序号" width="60" />
					<el-table-column fixed align="center" prop="repairOrderSn" label="报修单号" width="200" />
					<el-table-column align="center" prop="stationName" label="业主姓名" width="100" />
					<el-table-column align="center" prop="stationCode" label="电站编码" width="200">
						<template #default="scope">
							{{ scope.row.stationCode || '-' }}
						</template>
					</el-table-column>
					<el-table-column align="center" prop="stationPhone" label="业主联系方式" width="150">
						<template #default="scope">
							{{ scope.row.stationPhone ? getEncryptedPhone(scope.row.stationPhone, 'phone') || '-' : '-'
							}}
						</template>
					</el-table-column>
					<el-table-column align="center" prop="status" label="处理状态" width="100">
						<template #default="scope">
							{{ repairStatus[scope.row.status] || '-' }}
						</template>
					</el-table-column>
					<el-table-column align="center" prop="faultAppearance" label="故障现象" width="200">
						<template #default="scope">
							{{ scope.row.faultAppearance || '-' }}
						</template>
					</el-table-column>
					<el-table-column align="center" prop="faultDesc" label="问题描述" min-width="180">
						<template #default="scope">
							{{ scope.row.faultDesc || '-' }}
						</template>
					</el-table-column>
					<el-table-column align="center" prop="imgs" label="照片" width="240">
						<template #default="scope">
							<!-- <a :href="item" target="_blank" v-for="(item,index) in scope.row.imgs" :key="index">
								<img class="tdimg" :src="item" />
							</a> -->
							<a v-for="(item, index) in scope.row.imgs" :key="index">
								<img class="tdimg" :src="item"
									@click="showModel = true; imgArr = scope.row.imgs; carouselIndex = index" />
							</a>
						</template>
					</el-table-column>
					<el-table-column align="center" prop="opName" :label="`${ospName.OSP_NAME}`" width="200">
						<template #default="scope">
							{{ scope.row.opName || '-' }}
						</template>
					</el-table-column>
					<el-table-column align="center" prop="stationProvinceName" label="区域" width="200">
						<template #default="scope">
							{{ scope.row.stationProvinceName + scope.row.stationCityName + scope.row.stationRegionName
							}}
						</template>
					</el-table-column>
					<el-table-column align="center" prop="stationAddress" label="详细地址" width="200">
						<template #default="scope">
							{{ scope.row.stationAddress || '-' }}
						</template>
					</el-table-column>
					<el-table-column align="center" prop="source" label="报修方式" width="120">
						<template #default="scope">
							{{ repairSource[scope.row.source] || '-' }}
						</template>
					</el-table-column>
					<el-table-column align="center" prop="actualCloseTime" label="运维工单实际关单时间" width="200">
						<template #default="scope">
							{{ scope.row.actualCloseTime && scope.row.actualCloseTime.replace('T', ' ') || '-' }}
						</template>
					</el-table-column>
					<el-table-column align="center" prop="createdAt" label="报修时间" width="200">
						<template #default="scope">
							{{ scope.row.createdAt && scope.row.createdAt.replace('T', ' ') || '-' }}
						</template>
					</el-table-column>
					<el-table-column fixed="right" align="center" label="操作" width="160">
						<template #default="scope">
							<el-button :disabled="!limits.includes('lightoperation:apply:transfer')"
								v-if="scope.row.status === 'WAIT_HANDLE'" plain size="small" type="primary"
								@click="showApply(scope.row.repairOrderSn)">转入运维工单</el-button>
							<el-button :disabled="!limits.includes('lightoperation:apply:close')"
								v-if="scope.row.status === 'WAIT_HANDLE' || scope.row.status === 'HANDLED'" plain
								size="small" type="danger" @click="closeWorkOrder(scope.row)">关闭报修单</el-button>
							<span v-else>-</span>
						</template>
					</el-table-column>
				</el-table>
				<el-pagination class="cus-pages" v-if="totalElements" background
					layout="sizes, prev, pager, next, ->, total" :page-sizes="[10, 20, 30]"
					:page-size="formSearch.pageSize" :current-page="formSearch.pageNum" :total="totalElements"
					@sizeChange="changeSize" @currentChange="changeCurrent" />
			</div>
		</div>
		<el-dialog v-model="showModel" align-center draggable destroy-on-close :modal="false">
			<el-carousel height="60vh" :arrow="imgArr.length > 1 ? 'always' : 'never'" :autoplay="false"
				indicator-position="outside" :initial-index="carouselIndex">
				<el-carousel-item v-for="item in imgArr" :key="item">
					<div class="flex-column-center-center" style="height: 100%;">
						<img :src="item" style="max-width: 100%;max-height: 100%;" />
					</div>
				</el-carousel-item>
			</el-carousel>
		</el-dialog>
		<el-dialog v-model="dialogApply" title="转入运维工单" width="600px" draggable @close="dialogApply = false" center>
			<el-form class="cus-list-form" :model="applyObj" label-position="left" label-width="100px">
				<el-form-item label="报修单号">
					{{ applyObj.repairOrderSn }}
				</el-form-item>
				<el-form-item label="工单类型">
					<el-select v-model="applyObj.workOrderTypeId1" placeholder="选择工单类型" clearable
						@change="typeChange(1)">
						<el-option v-for="(item) in listType[0]" :label="item.workOrderType" :value="item.id"
							:key="item.id" />
					</el-select>
				</el-form-item>
				<el-form-item v-if="applyObj.workOrderTypeId1">
					<el-select v-model="applyObj.workOrderTypeId2" placeholder="选择工单类型" clearable
						@change="typeChange(2)">
						<el-option v-for="(item) in listType[1]" :label="item.workOrderType" :value="item.id"
							:key="item.id" />
					</el-select>
				</el-form-item>
				<el-form-item v-if="applyObj.workOrderTypeId2">
					<el-select v-model="applyObj.workOrderTypeId3" placeholder="选择工单类型" clearable>
						<el-option v-for="(item) in listType[2]" :label="item.workOrderType" :value="item.id"
							:key="item.id" />
					</el-select>
				</el-form-item>
			</el-form>
			<template #footer>
				<div class="dialog-footer">
					<el-button @click="dialogApply = false">取消</el-button>
					<el-button type="primary" @click="handleApply">提交</el-button>
				</div>
			</template>
		</el-dialog>
		<el-dialog v-model="closeOrderDialog" title="关闭客服工单" width="600px" draggable @close="dialogApply3 = false"
			center>
			<el-form :model="formClose">
				<p class="titleOrder">请确认是否关闭此报修单{{ closeOrderObj.repairOrderSn }}</p>
				<el-form-item class="selectStyle" v-if="closeOrderObj.status === 'HANDLED'">
					<el-date-picker v-model="formClose.actualCloseTime" placeholder="请选择运维工单实际关单时间" clearable
						style="width: 268px" type="datetime" format="YYYY-MM-DD HH:mm:ss"
						value-format="YYYY-MM-DD HH:mm:ss" :default-time="new Date()" />
				</el-form-item>
			</el-form>
			<template #footer>
				<div class="dialog-footer">
					<el-button @click="closeOrderDialog = false">取消</el-button>
					<el-button type="primary" @click="csOrderClose()">提交</el-button>
				</div>
			</template>
		</el-dialog>
	</div>
</template>

<script>
import API from '@/api/osp';
import _D from '@/edata/_osp_data';
import _ from 'lodash';
import dataEncryption from '@/utils/encryption'
// import ImgModel from '@/components/imgModel.vue';

export default {
	name: 'OperApply',
	data() {
		return {
			formSearch: {
				createEnd: '',
				createStart: '',
				linkMobile: '',
				linkName: '',
				opName: '',
				pageNum: 1,
				pageSize: 10,
				repairOrderSn: '',
				stationCode: '',
				status: '' //  工单状态:WAIT_HANDLE(待处理),HANDLED(已处理),CLOSED(已关闭)
			},
			closeOrderDialog: false,
			closeOrderObj: {},
			createTime: '',
			listArr: [],
			loading: false,
			totalPages: 0,
			totalElements: 0,
			repairStatus: _D.repairStatus,
			repairSource: _D.repairSource,
			limits: this.$route.meta.limits,
			imgArr: [],
			carouselIndex: 0,
			showModel: false,
			dialogApply: false,
			applyObj: {
				repairOrderSn: '',
				workOrderTypeId1: '',
				workOrderTypeId2: '',
				workOrderTypeId3: ''
			},
			formClose: {
				actualCloseTime: '',
			},
			ospName: _D.ospName,
			listType: [],
		};
	},

	components: {
		// ImgModel
	},

	mounted() {
		this.limits.includes('lightoperation:apply:list') && this.getList()
		this.limits.includes('lightoperation:apply:list') && this.getType(0)
	},

	methods: {
		// 日期选择
		dateChange(e) {
			if (e) {
				this.formSearch.createStart = e[0];
				this.formSearch.createEnd = e[1];
			} else {
				this.formSearch.createStart = '';
				this.formSearch.createEnd = '';
			}
		},

		// 切换分页
		changeCurrent(curr) {
			this.formSearch.pageNum = curr;
			this.getList();
		},

		// 每页条数
		changeSize(size) {
			this.formSearch.pageSize = size;
			this.getList();
		},

		queryList() {
			this.listArr = [];
			this.formSearch.pageNum = 1;
			this.getList();
		},

		showApply(repairOrderSn) {
			this.applyObj = this.$options.data.call(this).applyObj
			this.dialogApply = true;
			this.applyObj.repairOrderSn = repairOrderSn
		},

		typeChange(level) {
			const parentId = level === 1 ? this.applyObj.workOrderTypeId1 : this.applyObj.workOrderTypeId2
			this.getType(level, parentId)
		},

		getType(level, parentId = 0) {
			const params = {
				pageNum: 1,
				pageSize: 999,
				level: level + 1,
				parentId
			};
			API.orderTypeList(params).then(res => {
				if (res.success) {
					this.listType[level] = res.result.content;
				} else {
					this.listType[level] = [];
				}
			});
		},

		getList() {
			this.loading = true;
			const params = this.formSearch;
			API.repairList(params).then(res => {
				if (res.success) {
					this.listArr = res.result.content;
					this.totalPages = res.result.totalPages;
					this.totalElements = res.result.totalElements;
					this.loading = false;
				} else {
					this.loading = false;
					this.listArr = [];
					this.$message.error(res.error);
				}
			})
		},
		//数据加密
		getEncryptedPhone(data, type) {
			switch (type) {
				case 'phone':
					return dataEncryption.encryptData(data, 'phone');
				default:
					return '-';
			}
		},
		closeWorkOrder(item) {
			this.closeOrderDialog = true
			this.closeOrderObj = item
		},
		csOrderClose() {
			if (this.closeOrderObj.status === 'HANDLED') {
				const params = {
					actualCloseTime: this.formClose.actualCloseTime,
					repairOrderSn: this.closeOrderObj.repairOrderSn
				};
				if (params.actualCloseTime) {
					API.repairClose(params).then(res => {
						if (res.data.success) {
							this.$message.success('工单关闭成功');
							this.closeOrderDialog = false
							this.queryList()
						} else {
							this.$message.error(res.data.error);
						}
					})
				} else {
					this.$message.error('请填写实际关单时间');
				}

			} else {
				let params = {
					repairOrderSn: this.closeOrderObj.repairOrderSn
				}
				API.repairClose(params).then(res => {
					if (res.data.success) {
						this.$message.success('工单关闭成功');
						this.closeOrderDialog = false
						this.queryList()
					} else {
						this.$message.error(res.data.error);
					}
				})
			}
		},

		// 重置筛选条件
		onReset: _.throttle(
			function () {
				this.createTime = '';
				Object.assign(this.formSearch, this.$options.data.call(this).formSearch)
				this.$message.success("重置完成");
			},
			3000,
			{
				trailing: false
			}
		),

		// 导出
		exportList: _.throttle(
			function () {
				this.$confirm('请确认是否导出此筛选条件下的列表?', '提示', {
					confirmButtonText: '确定',
					cancelButtonText: '取消',
					type: 'warning'
				})
					.then(() => {
						const loading = this.$loading({
							text: '正在导出，请稍后'
						});
						const params = this.formSearch;
						API.repairReportdoExport(params)
							.then(res => {
								let binaryData = [];
								let link = document.createElement('a');
								binaryData.push(res);
								link.style.display = 'none';
								link.href = window.URL.createObjectURL(new Blob(binaryData));
								link.setAttribute('download', '业主报修列表.xlsx');
								document.body.appendChild(link);
								link.click();
								document.body.removeChild(link);
							})
							.then(() => {
								loading.close();
							})
							.catch(() => {
								loading.close();
								this.$message.error('导出失败');
							});
					})
					.catch(() => {
						// console.log('取消');
					});
			},
			3000,
			{
				trailing: false
			}
		),
		// 提交
		handleApply: _.throttle(
			function () {
				const params = this.applyObj;
				API.repairTransfer(params).then(res => {
					res = res.data
					if (res.success) {
						this.$message.success('提交成功');
						this.dialogApply = false
						this.queryList();
					} else {
						this.$message.error(res.error);
					}
				})
			},
			3000,
			{
				trailing: false
			}
		)
	}
};
</script>

<style lang="less" scoped>
@import '@/assets/style/_cus_header.less';
@import '@/assets/style/_cus_list.less';

.right {
	justify-content: flex-end;
}

.selectStyle {
	margin-top: 10px;

	:deep(.el-form-item__content) {
		flex-direction: column !important;
	}
}

.titleOrder {
	width: 100%;
	height: 30px;
	font-size: 16px;
	display: flex;
	align-items: center;
	justify-content: space-evenly;
}
</style>
