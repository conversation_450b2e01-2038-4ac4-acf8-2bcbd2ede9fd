<template>
  <div class="wrap">
    <div class="cus-header">
      <el-form :inline="true" :model="searchObj" class="demo-form-inline">
        <div class="cus-branch">
          <el-form-item label="发货仓库"><el-input class="cus-input-large" v-model="searchObj.suptWarehouseName" placeholder="请输入" clearable /></el-form-item>
          <el-form-item label="申请仓库"><el-input class="cus-input-large" v-model="searchObj.warehouseName" placeholder="请输入" clearable /></el-form-item>
          <el-form-item label="订单编号"><el-input class="cus-input-large" v-model="searchObj.orderCode" placeholder="请输入" clearable /></el-form-item>
          <el-form-item label="工单编号"><el-input class="cus-input-large" v-model="searchObj.woId" placeholder="请输入" clearable /></el-form-item>
          <el-form-item label="备件编码"><el-input class="cus-input-large" v-model="searchObj.materialCode" placeholder="请输入" clearable /></el-form-item>
          <el-form-item label="备件名称"><el-input class="cus-input-large" v-model="searchObj.materialDesc" placeholder="请输入" clearable /></el-form-item>
          <el-form-item label="收件人"><el-input class="cus-input-large" v-model="searchObj.linkMan" placeholder="请输入" clearable /></el-form-item>
          <el-form-item label="申请时间">
            <el-date-picker
                v-model="createDate"
                type="daterange"
                value-format="YYYY-MM-DD"
                start-placeholder="开始时间"
                end-placeholder="结束时间"
                @change="dateChange"
                clearable
            />
          </el-form-item>
        </div>
        <div class="cus-branch">
          <el-form-item>
            <el-button type="primary" @click="queryList">查询</el-button>
            <el-button type="default" @click="onReset">重置</el-button>
          </el-form-item>
        </div>
        <div class="cus-branch">
          <el-button type="success" @click="exportList"  v-show="limits.includes('sparePartMonitor:ordermonitor:export')">导出</el-button>
        </div>
      </el-form>
    </div>
    <div class="cus-main">
      <div class="cus-list" v-loading="loading">
        <el-table :data="listArr" class="cus-table">
          <el-table-column align="center" type="index" label="序号" width="60" />
          <el-table-column align="center" prop="orderCode" label="订单编号" width="160">
            <template #default="scope">
              {{ scope.row.orderCode}}
            </template>
          </el-table-column>
          <el-table-column align="center" prop="woId" label="工单编号" width="160">
            <template #default="scope">
              {{ scope.row.woId }}
            </template>
          </el-table-column>
          <el-table-column align="center" prop="orderType" label="订单类型" width="100">
            <template #default="scope">
              {{ scope.row.orderType}}
            </template>
          </el-table-column>
          <el-table-column align="center" prop="orderStatus" label="订单状态" width="120">
            <template #default="scope">
              {{ scope.row.orderStatus  }}
            </template>
          </el-table-column>
          <el-table-column align="center" prop="warehouseName" label="申请仓库" width="120">
            <template #default="scope">
              {{ scope.row.warehouseName }}
            </template>
          </el-table-column>
          <el-table-column align="center" prop="suptBranchName" label="分中心" width="100">
            <template #default="scope">
              {{ scope.row.suptBranchName }}
            </template>
          </el-table-column>
          <el-table-column align="center" prop="suptWarehouseName" label="发货仓库" width="160">
            <template #default="scope">
              {{ scope.row.suptWarehouseName}}
            </template>
          </el-table-column>
          <el-table-column align="center" prop="linkMan" label="收件人" width="160">
            <template #default="scope">
              {{ scope.row.linkMan}}
            </template>
          </el-table-column>
          <el-table-column align="center" prop="phone" label="收件电话" width="160">
            <template #default="scope">
              {{ scope.row.phone}}
            </template>
          </el-table-column>
          <el-table-column align="center" prop="receiveAddress" label="收件地址" width="160">
            <template #default="scope">
              {{ scope.row.receiveAddress}}
            </template>
          </el-table-column>
          <el-table-column align="center" prop="materialCode" label="备件编码" width="160">
            <template #default="scope">
              {{ scope.row.materialCode}}
            </template>
          </el-table-column>
          <el-table-column align="center" prop="materialDesc" label="备件名称" width="160">
            <template #default="scope">
              {{ scope.row.materialDesc}}
            </template>
          </el-table-column>
          <el-table-column align="center" prop="useStatus" label="使用状态" width="100">
            <template #default="scope">
              {{ scope.row.useStatus }}
            </template>
          </el-table-column>
          <el-table-column align="center" prop="requireAmount" label="申请数量" width="100">
            <template #default="scope">
              {{ scope.row.requireAmount }}
            </template>
          </el-table-column>
          <el-table-column align="center" prop="confirmSendAmount" label="发货数量" width="100">
            <template #default="scope">
              {{ scope.row.confirmSendAmount }}
            </template>
          </el-table-column>
          <el-table-column align="center" prop="cancelAmount" label="取消数量" width="100">
            <template #default="scope">
              {{ scope.row.cancelAmount  }}
            </template>
          </el-table-column>
          <el-table-column align="center" prop="deelAmount" label="匹配数量" width="100">
            <template #default="scope">
              {{ scope.row.deelAmount }}
            </template>
          </el-table-column>
          <el-table-column align="center" prop="submitDate" label="申请时间" width="160">
            <template #default="scope">
              {{ scope.row.submitDate  }}
            </template>
          </el-table-column>
          <el-table-column align="center" prop="sendDate" label="发货时间" width="160">
            <template #default="scope">
              {{ scope.row.sendDate }}
            </template>
          </el-table-column>
          <el-table-column align="center" prop="singDate" label="签收时间" width="160">
            <template #default="scope">
              {{ scope.row.singDate  }}
            </template>
          </el-table-column>
          <el-table-column align="center" prop="remarks" label="订单备注" width="160">
            <template #default="scope">
              {{ scope.row.remarks  }}
            </template>
          </el-table-column>
        </el-table>
        <el-pagination
            class="cus-pages"
            v-if="totalElements"
            background
            layout="sizes, prev, pager, next, ->, total"
            :page-sizes="[10, 20, 30]"
            :page-size="searchObj.rows"
            :current-page="searchObj.page"
            :total="totalElements"
            @sizeChange="changeSize"
            @currentChange="changeCurrent"
        />
      </div>
    </div>
  </div>
</template>

<script>
import API from '@/api/sparePartMonitor/index';
import _D from '@/edata/_osp_data';
import _ from 'lodash';

export default {
  name: 'Ordermonitor',
  data() {
    return {
      searchObj: {
        page: 1,
        rows: 10,
        id: '',
        suptWarehouseName: '',
        warehouseName: '',
        orderCode: '',
        partNo: '',
        materialName: '',
      },
      listArr: [],
      detailObj: {},
      loading: false,
      totalPages: 0,
      totalElements: 0,
      opList: _D.InveterFactoryArr,
      headers: {
        Authorization: `Bearer ${JSON.parse(window.localStorage.getItem('logins')).access_token}`
      },
      limits: this.$route.meta.limits,
      createDate:''
    };
  },

  mounted() {
    this.limits.includes('sparePartMonitor:ordermonitor:list') && this.getList();
  },

  methods: {
    // 日期选择
    dateChange(e) {
      if (e) {
        this.searchObj.submitDateStart = e[0];
        this.searchObj.submitDateEnd = e[1];
      } else {
        this.searchObj.submitDateStart = '';
        this.searchObj.submitDateEnd = '';
      }
    },
    // 切换分页
    changeCurrent(curr) {
      this.searchObj.page = curr;
      this.getList();
    },

    // 每页条数
    changeSize(size) {
      this.searchObj.rows = size;
      this.getList();
    },

    // 查询
    queryList() {
      this.listArr = [];
      this.searchObj.page = 1;
      this.getList();
    },

    // 获取列表
    getList() {
      this.loading = true;
      const params =JSON.parse(JSON.stringify(this.searchObj)) ;
      let page={
        'page':this.searchObj.page,
        'rows':this.searchObj.rows
      }
      delete params.page
      delete params.rows
      let data=params
      this.removeEmptyValues(data)
      API.sendGoodsList(page,data).then(res => {
        // console.log(res);
        if (res.data.success) {
          this.listArr = res.data.result.content;
          this.totalPages = res.data.result.totalPages;
          this.totalElements = res.data.result.totalElements;
          this.loading = false;
        } else {
          this.loading = false;
          this.listArr = [];
          this.$message.error(res.data.error);
        }
      });
    },
    // 对象 去除 value为空的键值对
    removeEmptyValues(obj) {
      for (const key in obj) {
        if (typeof obj[key] === 'object') {
          this.removeEmptyValues(obj[key]);
        }

        if (!obj[key] || (typeof obj[key] === 'object' && !Reflect.ownKeys(obj[key]).length)) {
          delete obj[key];
        }
      }
    },
    excelUpError(response) {
      console.log(response);
    },
    // 下载模板
    handleDown: _.throttle(
        function() {
          let domain = import.meta.env.VITE_APP_BASE_API;
          window.location.href = domain + '/hdsapi/module/info/downTemplate.do';
        },
        3000,
        {
          trailing: false
        }
    ),
    // 重置筛选条件
    onReset: _.throttle(
        function() {
          this.createDate=[]
          this.searchObj.submitDateStart=''
          this.searchObj.submitDateEnd=''
          Object.assign(this.searchObj, this.$options.data.call(this).searchObj);
          this.$message.success('重置完成');
        },
        3000,
        {
          trailing: false
        }
    ),
    // 导出项目列表
    exportList: _.throttle(
        function () {
          this.$confirm('请确认是否导出此筛选条件下的列表?', '提示', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
          })
              .then(() => {
                const loading = this.$loading({
                  text: '正在导出，请稍后'
                });
                const params = this.searchObj;
                API.sendGoodsExport(params)
                    .then(res => {
                      let binaryData = [];
                      let link = document.createElement('a');
                      let date = new Date().getTime()
                      binaryData.push(res);
                      link.style.display = 'none';
                      link.href = window.URL.createObjectURL(new Blob(binaryData));
                      link.setAttribute('download', `订单监控列表_${date}.xlsx`);
                      document.body.appendChild(link);
                      link.click();
                      document.body.removeChild(link);
                    })
                    .then(() => {
                      loading.close();
                    })
                    .catch(() => {
                      loading.close();
                      this.$message.error('导出失败');
                    });
              })
              .catch(() => {
                // console.log('取消');
              });
        },
        3000,
        {
          trailing: false
        }
    ),
  }
};
</script>

<style lang="less" scoped>
@import '@/assets/style/_cus_header.less';
@import '@/assets/style/_cus_list.less';
</style>

