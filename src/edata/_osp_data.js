/***
 * @Author: Lufy
 * @Date: 2022-08-24 09:41:09
 * @LastEditTime: 2022-08-24 09:41:09
 * @FilePath: /src/edata/_osp_data.js
 * @Description: OSP 数据字典
 */

export default {
  omStatus: {
    CONFIRMED: "确认暂估",
    CONFIRM_INCOME: "确认实际",
    CONFIRMED_SK: "确认收款",
  },
  ospName: {
    OSP_NAME: "运维商",
  },
  //电站模式
  detailMode: {
    HR: "BT",
    YX: "户用EPC(YX)",
    ZH: "户用EPC(ZH)",
    CHD_EPC: "户用EPC(HD)",
    EPC: "工商业EPC",
  },
  pmDetailMode: [
    {
      value: "HR", //华润
      label: "BT",
    },
    {
      value: "YX", //越秀
      label: "户用EPC(YX)",
    },
    {
      value: "ZH", //中核
      label: "户用EPC(ZH)",
    },
    {
      value: "ZCHD_EPC", //华电
      label: "户用EPC(HD)",
    },
    {
      value: "EPC", // 工商业EPC
      label: "工商业EPC",
    },
  ],
  /***
   * @description: 暂不运维电站
   */
  //审核状态
  noOpStationStatus: {
    WAIT_AUDIT: "待审核",
    AUDIT_REJECT: "审核驳回",
    AUDIT_OK: "审核通过",
  },
  /***
   * @description: 原材料-订单状态
   */
  rmOrderStatus: {
    WAIT_CENTER_AUDIT: "待分中心审核",
    WAIT_FINANCE_AUDIT: "待财务审核",
    AUDIT_REJECT: "审核驳回",
    WAIT_PAY: "待支付",
    COMPLETED: "已完成",
    CANCELED: "已取消",
  },
  /***
   * @description: 原材料-付款状态
   */
  rmPaymentStatus: {
    WAIT_FINANCE_CONFIRM: "待财务确认",
    PAYED: "已支付",
  },
  /***
   * @description: 原材料-发票状态
   */
  rmInvoiceStatus: {
    WAIT: "待开发票",
    SEND: "已开发票",
  },
  /***
   * @description: 原材料-去支付
   */
  goPay: {
    TRANSFER: "转账汇款",
  },
  /***
   * @description: 原材料-原因
   */
  rmReason: {
    OPERATE: "运营原因",
    OTHER: "其他原因",
  },
  /***
   * @description: 原材料-名称
   */
  rmName: {
    MODULE: "组件",
    INVERTER: "逆变器",
  },
  /***
   * @description: 原材料-节点
   */
  rmNode: {
    CREATE: "提交申请",
    UPDATE: "修改",
    CANCEL: "取消订单",
    CENTER_AUDIT_OK: "分中心审核通过",
    CENTER_AUDIT_REJECT: "分中心审核驳回",
    FINANCE_AUDIT_OK: "财务审核通过",
    FINANCE_AUDIT_REJECT: "财务审核驳回",
    PAY: "服务商支付",
    FINANCE_CONFIRM: "财务确认收款",
  },
  /***
   * @description: 原材料-收款账户
   */
  emAccount: {
    JHDH: "建行基本户",
  },
  /***
   * @description: 工单状态
   */
  OperSatusArr: {
    WAIT_DISPATCH: "已派单",
    DISPATCHED: "已派工",
    SERVICE: "服务中",
    COMPLETE: "已完工",
    CLOSED: "已关闭",
  },
  operSatusArr: [
    {
      value: "WAIT_DISPATCH",
      label: "已派单",
    },
    {
      value: "DISPATCHED",
      label: "已派工",
    },
    {
      value: "SERVICE",
      label: "服务中",
    },
    {
      value: "COMPLETE",
      label: "已完工",
    },
    {
      value: "CLOSED",
      label: "已关闭",
    },
  ],
  /***
   * @description: 工单来源
   */
  SourceArr: {
    STATION_ALARM: "电站报警",
    OWNER_REPAIR: "业主报修",
    CUSTOMER_SERVICE: "客服工单",
    LOW_POWER_STATION: "低效电站",
    TRIPPING_OPERATION_STATION: "频繁跳闸工单",
  },
  sourceList: [
    {
      value: "STATION_ALARM",
      label: "电站报警",
    },
    {
      value: "OWNER_REPAIR",
      label: "业主报修",
    },
    {
      value: "CUSTOMER_SERVICE",
      label: "客服工单",
    },
    {
      value: "LOW_POWER_STATION",
      label: "低效电站",
    },
    {
      value: "TRIPPING_OPERATION_STATION",
      label: "频繁跳闸工单",
    },
  ],
  /***
   * @description: 工单流程来源
   */
  recordSource: {
    MERCHANT: "运维商",
    MINI_PROGRAM: "运维商",
    HDS: "总部",
  },

  /***
   * @description: 服务人员审核状态
   */
  StaffAuditStatusArr: {
    // 'ADOPT': '通过',
    // 'REJECTED': '驳回',
    CHECKPENDING: "待审核",
    ADOPT: "审核通过",
    REJECTED: "审核驳回",
    TWOADOPT: "审核通过",
    TWOREJECTED: "审核驳回",
  },
  staffAuditStatusArr: [
    {
      value: "CHECKPENDING",
      label: "待审核",
    },
    {
      value: "TWOADOPT" || "ADOPT",
      label: "审核通过",
    },
    {
      value: "TWOREJECTED" || "REJECTED",
      label: "审核驳回",
    },
  ],
  /***
   * @description: 签约管理-公司类型
   */
  CompanyTypeArr: {
    1: "上市公司",
    2: "股份有限公司",
    3: "有限责任公司",
    4: "合伙经营",
    5: "个人独资",
    6: "个体户",
    7: "国有企业",
    8: "集体企业",
  },
  /***
   * @description: 签约管理-服务协议模式
   */
  ServiceModeArr: {
    WHOLE: "大包模式",
    BY_ORDER: "按单模式",
  },
  /***
   * @description: 签约管理-纳税人类型
   */
  TaxpayerTypeArr: {
    1: "一般纳税人",
    2: "小规模纳税人",
    3: "个体户",
    4: "个人",
  },
  /***

   * @description: 签约管理-服务商类型
   */
  identityType: {
    STATION_SERVICE: "建站服务商",
    THIRD_SERVICE: "专业运维商",
  },
  /***
   * @description: 签约管理-审核状态
   */
  toExamineSatus: {
    0: "待审核", //申请加盟,待审核
    3: "待签合同", //终审通过,待签合同
    5: "待续约",
    4: "注册审核驳回", //终审驳回
    6: "已签合同", //运营中
    7: "待添加工程师", //待添加服务兵
    8: "终止运维", //终止运营
  },
  /***
   * @description: 签约管理-签约提醒
   */
  isRemindSatus: {
    0: "已提醒", //申请加盟,待审核
    1: "未提醒", //终审通过,待签合同
  },
  /***
   * @description: 签约管理-终止运维审核状态
   */
  suspendReview: {
    0: "终止待审核",
    3: "终止审核驳回",
    4: "终止审核通过", //终止审核通过：对应“终止运维”
  },
  /***
   * @description: 签约管理-状态
   */
  signingSatus: {
    0: "待审核", //申请加盟,待审核
    1: "待资质审核",
    2: "资质审核驳回",
    3: "加盟待签合同", //终审通过,待签合同
    5: "待续约", //终审通过,待签合同
    4: "加盟审核驳回", //终审驳回
    6: "加盟已签合同", //运营中
    7: "待添加工程师", //待添加服务兵
    8: "终止运营", //终止运营
  },
  /***
   * @description: 签约管理-续签状态
   */
  renewalOfContract: {
    0: "续约待审核",
    3: "续约待签合同",
    4: "续约审核驳回",
    6: "续约合同已签订",
    8: "不续约审核通过",
  },
  /***
   * @description: 签约管理-状态
   */
  ProvSatusArr: {
    0: "待审核",
    1: "审核通过",
    5: "签订合同",
    6: "运营中",
    "-1": "审核驳回",
  },
  /***
   * @description: 签约管理-终止运维
   */
  businessTypes: {
    1: "户用运维",
    2: "工商业运维",
    3: "工商业运维",
  },
  /***
   * @description: 签约管理-终止运维
   */
  stopStatus: {
    0: "终止待审核",
    1: "终止审核驳回",
    8: "终止审核通过",
  },
  /**
   * @description: 终止状态
   */
  stopStatusExtend: {
      INIT: "无",
      WAIT_AUDIT: "待审核",
      WAIT_SIGN: "待签署终止协议",
      STOP: "已终止",
      AUDIT_REJECT: "已驳回",
      REVOCATION: "已撤回"
  },
  /***
   * @description: 工商业项目运维商管理-续签状态
   */

  maintenance: {
    0: "续约待审核",
    1: "续约审核通过",
    2: "续约审核驳回",
    3: "不续约审核通过",
  },
  /***
   * @description: 员工管理-员工状态
   */
  regionTypeArr: {
    MASTER: "主区域",
    EXPAND: "扩充区域",
  },
  /***
   * @description: 员工管理-员工状态
   */
  regionStatus: {
    WAIT_AUDIT: "待审核",
    REJECT: "已驳回",
    WAIT_SIGN: "待签合同",
    WAIT_PAY: "待支付保证金",
    ENABLE: "已授权",
    DISABLE: "禁用",
  },
  /***
   * @description: 员工管理-员工状态
   */
  StaffStatusArr: {
    ONTHEJOB: "在职",
    TOBEEMPLOYED: "待入职",
    QUIT: "离职",
  },
  /***
   * @description: 告警码管理-逆变器厂家
   */
  InveterFactoryArr: {
    JL: "锦浪",
    ASW: "爱士惟",
  },
  /***
   * @description: 告警码管理-告警码类型
   */
  FaultCodeTypeArr: {
    WARN_CODE: "告警码",
    ERROR_CODE: "故障码",
  },
  /***
   * @description: 告警码管理-规则生效
   */
  RuleStatusArr: {
    ENABLE: "启用",
    DISABLE: "停用",
  },
  /***
   * @description: 告警码管理-等级类型
   */
  LevelTypeArr: {
    alarm: "告警",
    fault: "故障",
  },
  /***
   * @description: 结算管理-结算状态
   */
  settlementArr: {
    SETTLED: "已结算",
    TOBESETTLED: "待结算",
  },
  /****
   * @description: 月份
   */
  monthList: [
    {
      value: "1",
      label: "一月",
    },
    {
      value: "2",
      label: "二月",
    },
    {
      value: "3",
      label: "三月",
    },
    {
      value: "4",
      label: "四月",
    },
    {
      value: "5",
      label: "五月",
    },
    {
      value: "6",
      label: "六月",
    },
    {
      value: "7",
      label: "七月",
    },
    {
      value: "8",
      label: "八月",
    },
    {
      value: "9",
      label: "九月",
    },
    {
      value: "10",
      label: "十月",
    },
    {
      value: "11",
      label: "十一月",
    },
    {
      value: "12",
      label: "十二月",
    },
  ],
  /***
   * @description: 运维管理-业主报修-状态
   */
  repairStatus: {
    WAIT_HANDLE: "待处理",
    HANDLED: "已处理",
    CLOSED: "已关闭",
  },
  /***
   * @description: 运维管理-业主报修-来源
   */
  repairSource: {
    WX_APPLET: "微信小程序",
  },
  /***
   * @description: 运维管理-业主报修-状态
   */
  csOrderStatus: {
    WAIT_HANDLE: "待处理",
    HANDLED: "已转入",
    CLOSED: "已关闭",
  },
  /***
   * @description: 结算管理-结算状态
   */
  settleStatusList: [
    {
      value: "WAITING_PAY",
      label: "待付款",
    },
    {
      value: "HAD_PAY",
      label: "已结算",
    },
  ],
  settleStatus: {
    WAITING_PAY: "待付款",
    HAD_PAY: "已结算",
  },
  /***
   * @description: 结算管理-结算状态
   */
  confirmStatusList: [
    {
      value: "WAITING_CONFIRM",
      label: "待确认",
    },
    {
      value: "HAD_CONFIRM",
      label: "已确认",
    },
  ],
  confirmStatus: {
    WAITING_CONFIRM: "待确认",
    HAD_CONFIRM: "已确认",
  },
  /***
   * @description: 服务商 正向政策列表- 正向政策类型:
   */
  positiveStimulateTypeList: [
    {
      value: "POSITIVE_ONE",
      label: "低效整改",
    },
    {
      value: "POSITIVE_TWO",
      label: "低效占比",
    },
    {
      value: "POSITIVE_THREE",
      label: "一单一议",
    },
    {
      value: "POSITIVE_NOTIFICATION",
      label: "政策兑现通报(+)",
    },
  ],
  positiveStimulateType: {
    POSITIVE_ONE: "低效整改",
    POSITIVE_TWO: "低效占比",
    POSITIVE_THREE: "一单一议",
    POSITIVE_NOTIFICATION: "政策兑现通报(+)",
  },
  /***
   * @description: 服务商 正向政策列表-渠道来源:
   */
  positiveSource: {
    SHT: "商户通",
    HDS: "HDS",
  },
  /***
   * @description: 服务商 正向政策列表-超期:
   */
  exceedStatus: {
    EXPIRED: "超期",
    NOT_EXPIRED: "非超期",
  },
  /***
   * @description: 服务商 负向政策列表- 负向政策类型:
   */
  negativeStimulateTypeList: [
    {
      value: "NEGATIVE_ONE",
      label: "超期考核",
    },
    {
      value: "NEGATIVE_TWO",
      label: "闭环率考核",
    },
    {
      value: "NEGATIVE_THREE",
      label: "巡检考核",
    },
    {
      value: "NEGATIVE_FOUR",
      label: "反馈规范性考核",
    },
    {
      value: "NEGATIVE_NOTIFICATION",
      label: "政策兑现通报(-)",
    },
  ],
  negativeStimulateType: {
    NEGATIVE_ONE: "超期考核",
    NEGATIVE_TWO: "闭环率考核",
    NEGATIVE_THREE: "巡检考核",
    NEGATIVE_FOUR: "反馈规范性考核",
    NEGATIVE_NOTIFICATION: "政策兑现通报(-)",
  },
  //服务商 正向政策列表-审核状态
  positiveAuditStatusList: {
    WAIT_PRELIMINARY_AUDI: "待初审",
    PRELIMINARY_AUDI_REJECT: "初审驳回",
    WAIT_FIRST_AUDIT: "待一审",
    FIRST_AUDIT_REJECT: "一审驳回",
    WAIT_SECOND_AUDIT: "待二审",
    SECOND_AUDIT_REJECT: "二审驳回",
    SECOND_AUDIT_PASS: "二审通过",
    WAIT_THIRD_AUDIT: "待三审",
    THIRD_AUDIT_REJECT: "三审驳回",
    THIRD_AUDIT_PASS: "三审通过",
  },
  //服务商 负向政策列表-审核状态
  negativeAuditStatusList: {
    WAIT_FIRST_AUDIT: "待一审",
    FIRST_AUDIT_REJECT: "一审驳回",
    WAIT_SECOND_AUDIT: "待二审",
    SECOND_AUDIT_REJECT: "二审驳回",
    SECOND_AUDIT_PASS: "二审通过",
  },
  /***
   * @description: 结算管理-账单状态
   */
  statusCountList: {
    WAITING_CONFIRM: "待确认",
    WAITING_PAY: "待付款",
    HAD_PAY: "已结算",
  },

  /***
   * @description: 结算管理-运维记账类型
   */
  operationIncomeType: {
    A51: "项目公司运维成本暂估",
    A48: "运维收入和成本暂估",
  },
  detailStatus: {
    ENABLE: "已完成",
  },

  detStationType: {
    COMMON: "普通户用",
    HOUSEHOLD: "户用租赁",
    PUB_BUILD: "公共租赁",
    WHOLE_VILLAGE: "整村推进",
  },
  detFieldMethod: {
    COMPANY: "公司备案",
    HOUSEHOLD: "户用备案",
  },
  opType: {
    STATION_SERVICE: "建站服务商",
    THIRD_SERVICE: "专业运维服务商",
  },
  subCenterList: [
    {
      value: "GFBJ",
      label: "北京",
    },
    {
      value: "GFCC",
      label: "长春",
    },
    {
      value: "GFCD",
      label: "成都",
    },
    {
      value: "GFCQ",
      label: "重庆",
    },
    {
      value: "GFCS",
      label: "长沙",
    },
    {
      value: "GFDL",
      label: "大连",
    },
    {
      value: "GFFZ",
      label: "福州",
    },
    {
      value: "GFGY",
      label: "贵阳",
    },
    {
      value: "GFGZ",
      label: "广州",
    },
    {
      value: "GFHEB",
      label: "哈尔滨",
    },
    {
      value: "GFHF",
      label: "合肥",
    },
    {
      value: "GFHHHT",
      label: "内蒙",
    },
    {
      value: "GFHK",
      label: "海口",
    },
    {
      value: "GFHZ",
      label: "杭州",
    },
    {
      value: "GFJIN",
      label: "济宁",
    },
    {
      value: "GFJN",
      label: "济南",
    },
    {
      value: "GFJZ",
      label: "锦州",
    },
    {
      value: "GFKM",
      label: "昆明",
    },
    {
      value: "GFLZ",
      label: "兰州",
    },
    {
      value: "GFNB",
      label: "宁波",
    },
    {
      value: "GFNC",
      label: "南昌",
    },
    {
      value: "GFNJ",
      label: "南京",
    },
    {
      value: "GFNN",
      label: "南宁",
    },
    {
      value: "GFQD",
      label: "青岛",
    },
    {
      value: "GFSH",
      label: "上海",
    },
    {
      value: "GFSJZ",
      label: "石家庄",
    },
    {
      value: "GFSY",
      label: "沈阳",
    },
    {
      value: "GFSZ",
      label: "深圳",
    },
    {
      value: "GFTJ",
      label: "天津",
    },
    {
      value: "GFTS",
      label: "唐山",
    },
    {
      value: "GFTY",
      label: "太原",
    },
    {
      value: "GFWH",
      label: "武汉",
    },
    {
      value: "GFWX",
      label: "无锡",
    },
    {
      value: "GFXA",
      label: "西安",
    },
    {
      value: "GFXF",
      label: "襄樊",
    },
    {
      value: "GFXJ",
      label: "新疆",
    },
    {
      value: "GFXM",
      label: "厦门",
    },
    {
      value: "GFXN",
      label: "西宁",
    },
    {
      value: "GFXZ",
      label: "徐州",
    },
    {
      value: "GFYC",
      label: "银川",
    },
    {
      value: "GFYT",
      label: "烟台",
    },
    {
      value: "GFZZ",
      label: "郑州",
    },
  ],
  businessType: {
    1:'户用',
    2: "工商业运维",
    3: "工商业运维",
  },
  businessData: [
    // {
    //   value: '1',
    //   label: '户用',
    // },
    {
      value: 2 || 3,
      label: "工商业运维",
    },
  ],
  serviceSatus: {
    0: "项目入驻待审核",
    1: "项目入驻审核通过",
    2: "项目入驻审核驳回",
    5: "待续约",
    8: "已终止",
  },
  toServiceArea: {
    WAIT_AUDIT: "待审核",
    ENABLE: "已授权",
  },
  changeSatus: {
    0: "待审核",
    1: "终审通过",
    2: "终审驳回",
  },
  importSatus: {
    ENABLE: "商务审核通过",
  },
  oamPositiveStimulateTypeList: [
    {
      value: "POSITIVE_ONE",
      label: "一单一议",
    },
    {
      value: "POSITIVE_TWO",
      label: "技术改进",
    },
  ],
  oamPositiveStimulateType: {
    POSITIVE_ONE: "一单一议",
    POSITIVE_TWO: "技术改进",
  },
  oamNegativeStimulateTypeList: [
    {
      value: "NEGATIVE_ONE",
      label: "运维服务时效考核",
    },
    {
      value: "NEGATIVE_TWO",
      label: "安全目标考核",
    },
    {
      value: "NEGATIVE_THREE",
      label: "年利用小时数考核",
    },
    {
      value: "NEGATIVE_FOUR",
      label: "生产管理指标考核",
    },
  ],
  oamNegativeStimulateType: {
    NEGATIVE_ONE: "运维服务时效考核",
    NEGATIVE_TWO: "安全目标考核",
    NEGATIVE_THREE: "年利用小时数考核",
    NEGATIVE_FOUR: "生产管理指标考核",
  },
  dutyType: {
    OPERATIONS_ENGINEER: "运维工程师",
    OPERATIONS_SUPERVISOR: "运维主管",
    OPERATION_SCHEDULING: "运维调度",
  },
  /****
   * @description: 电站类型
   */
  stationTypeList: [
    {
      value: "COMMON",
      label: "普通户用",
    },
    {
      value: "HOUSEHOLD",
      label: "户用租赁",
    },
    {
      value: "PUB_BUILD",
      label: "公共租赁",
    },
    {
      value: "WHOLE_VILLAGE",
      label: "整村推进",
    },
  ],
  pmDetFieldMethod: [
    {
      value: "COMPANY",
      label: "公司备案",
    },
    {
      value: "HOUSEHOLD",
      label: "户用备案",
    },
  ],

  pmStationTypeList: [
    {
      value: "COMMON",
      label: "普通户用",
    },
    {
      value: "HOUSEHOLD",
      label: "户用租赁",
    },
    {
      value: "PUB_BUILD",
      label: "公共租赁",
    },
    {
      value: "WHOLE_VILLAGE",
      label: "整村推进",
    },
    {
      value: "CM",
      label: "工商业",
    },
  ],
  pmRegionStatus: {
    ENABLE: "已完成",
  },
  pmDetStationType: {
    COMMON: "普通户用",
    HOUSEHOLD: "户用租赁",
    PUB_BUILD: "公共租赁",
    WHOLE_VILLAGE: "整村推进",
    CM: "工商业",
  },
  pmSpecialFlag: {
    PF_1th: "浦银",
    PY_ALL: "浦银EPC",
    ZH_1th: "中核股转",
    ZH_ALL: "中核EPC",
    YX_EPC: "越秀",
    YX_ALL: "越秀EPC",
    // YX_EPC1: "越秀EPC",
    YX_GZ: "越秀股转",
    CHD_EPC: "华电",
    NH: "纳晖",
    CLIENT_BUY_BACK: "客户回购",
    PF: "浦银",
    DH_EPC: "顶好",
    ZH_PHASE_1: "中核一期",
    ZH_PHASE_2: "中核二期",
  },
  pmSpecialFlag2: {
    PF_1th: "浦银",
    PF_2th: "浦银EPC",
    PF_3th: "浦银EPC",
    PF_4th: "浦银EPC",
    PF_5th: "浦银EPC",
    PF_6th: "浦银EPC",
    PF_7th: "浦银EPC",
    PF_8th: "浦银EPC",
    PF_9th: "浦银EPC",
    PF_10th: "浦银EPC",
    PF_11th: "浦银EPC",
    PF_12th: "浦银EPC",
    PF_13th: "浦银EPC",
    PF_14th: "浦银EPC",
    PF_15th: "浦银EPC",
    PF_16th: "浦银EPC",
    ZH_1th: "中核股转",
    ZH_PHASE_1: "中核一期",
    ZH_PHASE_2: "中核二期",
    ZH_EPC_1th: "中核EPC",
    ZH_EPC: "中核EPC",
    YX_EPC: "越秀",
    YX_GZ: "越秀股转",
    YX_EPC1: "越秀EPC",
    DH_EPC: "顶好",
    CHD_EPC: "华电",
    NH: "纳晖",
    CLIENT_BUY_BACK: "客户回购",
    NOT_DIS: "--",
  },
  pmHouseTypeList: {
    flat: "平顶屋",
    slope: "斜顶屋",
    ground: "院内地面",
    hwdm: "户外地面",
    yghb: "渔光互补",
    nghb: "农光互补",
    ygf: "阳光房",
    epcSteel: "彩钢瓦",
    epcBeton: "混凝土",
    epcFloor: "地面",
    double_slope: "双面坡",
    enterprise_one: "企业厂房-彩钢瓦屋顶",
  },
  pmInstallList: {
    epcFixture: "夹具",
    epcGroundSupport: "地面支架",
    epcPrefabricate: "预制基础",
    enterprise_one_install: "厂房-彩钢瓦-平铺",
    flatSun: "平顶屋-阳光屋顶",
    flatToBolt: "平屋顶-平改坡（膨胀螺栓固定）",
    flatToBase: "平屋顶-平改坡（底梁固定）",
    flatToWeight: "平屋顶-平改坡（配重块）",
    flatBolt: "平屋顶-膨胀螺栓固定",
    flatWeight: "平屋顶-配重块",
    groundPileFix: "院内地面-柱桩固定",
    waterFloating: "水面漂浮",
    pileFilx: "柱桩固定",
    slopePull: "斜屋顶-前拉后拽",
    slopePullShort: "斜屋顶-前拉后拽+短柱",
    sunlightRoomFix: "阳光房-化学锚栓固定/膨胀螺栓",
    slopeHook: "斜屋顶-挂钩固定安装",
    slope_one: "双面坡-南北坡-前拉后拽",
    slope_two: "双面坡-南北坡-前拉后拽+短柱",
    slope_three: "双面坡-南北坡-双坡挂钩",
    slope_four: "双面坡-南北坡-双坡挂钩+水槽",
    slope_five: "双面坡-东西坡-前拉后拽",
    slope_six: "双面坡-东西坡-前拉后拽+短柱",
    slope_seven: "双面坡-东西坡-双坡挂钩",
    slope_eight: "双面坡-东西坡-双坡挂钩+水槽",
  },
  contractType: [
    {
      value: "DOUBLE",
      label: "双方合同",
    },
    {
      value: "THREE",
      label: "三方合同",
    },
    {
      value: "MORE",
      label: "多方合同",
    },
  ],
  contractStatus: {
    DOUBLE: "双方合同",
    THREE: "三方合同",
    MORE: "多方合同",
  },
  property: [
    {
      value: "PF_1th",
      label: "浦银",
    },
    {
      value: "PY_ALL",
      label: "浦银EPC",
    },
    {
      value: "ZH_1th",
      label: "中核股转",
    },
    {
      value: "ZH_ALL",
      label: "中核EPC",
    },
    {
      value: "YX_EPC",
      label: "越秀",
    },
    {
      value: "YX_ALL",
      label: "越秀EPC",
    },
    {
      value: "YX_GZ",
      label: "越秀股转",
    },
    {
      value: "NH",
      label: "纳晖",
    },
    {
      value: "CHD_EPC",
      label: "华电",
    },
    {
      value: "CLIENT_BUY_BACK",
      label: "客户回购",
    },
    {
      value: "NH",
      label: "纳晖",
    },
    {
      value: "PF",
      label: "浦银",
    },
    {
      value: "DH_EPC",
      label: "顶好",
    },
    {
      value: "ZH_PHASE_1",
      label: "中核一期",
    },
    {
      value: "ZH_PHASE_2",
      label: "中核二期",
    }
  ],
  propertyStatus: {
    SINGED: "已签约",
    NO_SIGNED: "未签约",
    STOP_SINGED: "终止签约",
    OUR: "我方数据",
    CAPITAL: "资方数据",
  },
  reconciliationForm: {
    OUR: "我方数据",
    CAPITAL: "资方数据",
  },
  changeDataStatus: [
    {
      value: "WAITE_AUDIT",
      label: "待中心经理审核",
    },
    {
      value: "AUDIT_REJECT",
      label: "审核驳回",
    },
    {
      value: "WITH_TECHNICAL_ACCEPTANCE",
      label: "待技术验收",
    },
    {
      value: "TECHNICAL_ACCEPTANCE_REJECT",
      label: "技术验收驳回",
    },
    {
      value: "TECHNICAL_ACCEPTANCE_OK",
      label: "验收通过",
    },
    {
      value: "STOP",
      label: "流程终止",
    },
  ],
  changeDataStatu: {
    WAITE_AUDIT: "待中心经理审核",
    AUDIT_REJECT: "审核驳回",
    WITH_TECHNICAL_ACCEPTANCE: "待技术验收",
    TECHNICAL_ACCEPTANCE_REJECT: "技术验收驳回",
    TECHNICAL_ACCEPTANCE_OK: "验收通过",
    STOP: "流程终止",
  },
  recommendation: {
    AUDIT_OK: "审核通过",
    AUDIT_REJECT: "审核驳回",
    ACCEPTANCE_OK: "验收通过",
    ACCEPTANCE_REJECT: "验收驳回",
  },
  infoCategory: {
    OPERATION: "光伏运维",
  },
  changeInverter: {
    INVERTER_CHANGE: "更改逆变器序列号",
  },
  infoContent: {
    CREATE: "创建提报信息",
    AUDIT: "平台审核",
    ACCEPTANCE: "技术验收",
    UPDATE: "提报信息更新",
    STOP: "流程终止",
    AUDIT_REJECT: "审核驳回",
  },
  /***
   * @description: 运维收入成本配置所属资产
   */
  // rcSpecialFlag: {
  //   PF_1TH: "浦银",
  //   PY_ALL: "浦银EPC",
  //   ZH_1TH: "中核股转",
  //   ZH_ALL: "中核EPC",
  //   YX_EPC: "越秀",
  //   YX_GZ: "越秀股转",
  //   YX_EPC1: "越秀EPC",
  //   NH: "纳晖",
  // },
  /***
   * @description: 运维收入成本配置模式
   */
  // rcMode: {
  //   HR: "BT", //华润
  //   YX: "户用EPC(越秀)", //越秀
  //   ZH: "户用EPC(中核)", //中核
  //   EPC: "工商业EPC", // 工商业EPC
  // },
  // rcModeList: [
  //   {
  //     value: "HR",
  //     label: "BT",
  //   },
  //   {
  //     value: "YX",
  //     label: "户用EPC(越秀)",
  //   },
  //   {
  //     value: "ZH",
  //     label: "户用EPC(中核)",
  //   },
  //   {
  //     value: "EPC",
  //     label: "工商业EPC",
  //   },
  // ],
  /***
   * @description: 运维收入成本配置状态
   */
  rcStatus: {
    WAITE_AUDIT: "待审核", //华润
    AUDIT_REJECT: "审核驳回", //越秀
    AUDIT_OK: "审核通过", //中核
  },
  rcStatusList: [
    {
      value: "WAITE_AUDIT",
      label: "待审核",
    },
    {
      value: "AUDIT_REJECT",
      label: "审核驳回",
    },
    {
      value: "AUDIT_OK",
      label: "审核通过",
    },
  ],
  /***
   * @description: 运维收入成本配置安装类型
   */
  rcType: {
    ZLS: "阵列式",
    GSY: "工商业",
    SMP: "双面坡",
    TYS: "庭院式",
    YGF: "阳光房",
  },
  rcTypeList: [
    {
      value: "ZLS",
      label: "阵列式",
    },
    {
      value: "GSY",
      label: "工商业",
    },
    {
      value: "SMP",
      label: "双面坡",
    },
    {
      value: "TYS",
      label: "庭院式",
    },
    {
      value: "YGF",
      label: "阳光房",
    },
  ],
  ospBusinessType: {
    1: "户用",
    2: "工商业",
  },
  ospBusinessTypeList: [
    {
      value: 1,
      label: "户用",
    },
    {
      value: 2,
      label: "工商业",
    },
  ],
  oamRegionStatus: {
    INIT: "待提交审核",
    WAIT_AUDIT: "待审核",
    AUDIT_REJECT: "驳回",
    WAIT_CONFIG: "审核完成待配方案",
    WAIT_ORDER: "待下单",
    ROADWORKING: "施工中",
    ROADWORK_CONFIRM: "施工信息待确认", //待服务商确认
    ROADWORK_REJECT: "施工信息驳回", //服务商操作
    COMPLETE_WAIT_AUDIT: "完工确认审核",
    COMPLETE_AUDIT_REJECT: "完工确认审核驳回",
    WAIT_TECH_CHECK: "待技术审核", // 总部审核
    APPLY_CHECK: "施工完成待申请并网验收", // 总部审核
    TECH_CHECK_REJECT: "技术审核驳回",
    WAIT_CHECK: "待验收", // 历史数据改为待技术审核
    WAIT_UPLOAD_GRID_INFO: "待提交并网验收资料",
    WAIT_FIRST_AUDIT: "待商务审核", // 总部审核
    FIRST_AUDIT_REJECT: "商务审核驳回",
    OFF_LINE_CHECK_REJECT: "现场验收驳回",
    WAIT_FINAL_AUDIT: "待终审二验",
    FINAL_AUDIT_REJECT: "终审二验驳回",
    ENABLE: "已完成",
    DISABLE: "删除",
    STOP: "已终止",
    REPURCHASE: "已回购",
    OFF_LINE_INIT: "待提交整改信息",
    WAIT_OFF_LINE_CHECK: "待现场验收", // * At 2024-04-23 新增流程
  },
  signingType: {
    GYB: "光E宝",
    YL: "银联",
  },
  /**
   * @description: 光E宝签约状态
   */
  eSignStatus: {
    NO_SIGN: "未签约",
    SIGNED: "已签约 ",
    ALLOW_SIGN: "可签约",
    NO_NEED_SIGN: "无需签约"
  },
  signingStatus: {
    CREATE: "已生成",
    INIT: "初始化",
    CREATE_FAIL: "生成失败",
    CREATE_ERROR: "生成错误",
    WAIT_SETTLE: "待结算",
    PAYING: "支付中",
    PAY_FAIL: "支付失败",
    PAY_SUCCESS: "支付成功",
    RE_PAY: "已作废",
  },
  oamRegionStatusList:[
    {
      value: "INIT",
      label: "待提交审核",
    },
    {
      value: "WAIT_AUDIT",
      label: "待审核",
    },
    {
      value: "AUDIT_REJECT",
      label: "驳回",
    },
    {
      value: "WAIT_CONFIG",
      label: "审核完成待配方案",
    },
    {
      value: "WAIT_ORDER",
      label: "待下单",
    },
    {
      value: "ROADWORKING",
      label: "施工中",
    },
    {
      value: "ROADWORK_CONFIRM",
      label: "施工信息待确认",
    },
    {
      value: "ROADWORK_REJECT",
      label: "施工信息驳回",
    },
    {
      value: "COMPLETE_WAIT_AUDIT",
      label: "完工确认审核",
    },
    {
      value: "COMPLETE_AUDIT_REJECT",
      label: "完工确认审核驳回",
    },
    {
      value: "WAIT_TECH_CHECK",
      label: "待技术审核",
    },
    {
      value: "APPLY_CHECK",
      label: "施工完成待申请并网验收",
    },
    {
      value: "TECH_CHECK_REJECT",
      label: "技术审核驳回",
    },
    {
      value: "WAIT_CHECK",
      label: "待验收",
    },

    {
      value: "WAIT_UPLOAD_GRID_INFO",
      label: "待提交并网验收资料",
    },
    {
      value: "WAIT_FIRST_AUDIT",
      label: "待商务审核",
    },
    {
      value: "FIRST_AUDIT_REJECT",
      label: "商务审核驳回",
    },
    {
      value: "OFF_LINE_CHECK_REJECT",
      label: "现场验收驳回",
    },
    {
      value: "WAIT_FINAL_AUDIT",
      label: "待终审二验",
    },
    {
      value: "FINAL_AUDIT_REJECT",
      label: "终审二验驳回",
    },
    {
      value: "ENABLE",
      label: "已完成",
    },
    {
      value: "DISABLE",
      label: "删除",
    },
    {
      value: "STOP",
      label: "已终止",
    },
    {
      value: "REPURCHASE",
      label: "已回购",
    },
    {
      value: "OFF_LINE_INIT",
      label: "待提交整改信息",
    },
    {
      value: "WAIT_OFF_LINE_CHECK",
      label: "待现场验收",
    },
  ],
  alarmStatusType: {
    ALARMED: "报警中",
    CLOSED: "已关闭",
  },
  alarmStatusList: [
    {
      value: "ALARMED",
      label: "报警中",
    },
    {
      value: "CLOSED",
      label: "已关闭",
    },
  ],
  qualsSatus: {
    WAIT_AUDI: "待审核",
    AUDI_OK: "审核通过",
    AUDI_REJECT: "审核驳回",
  },
  appealStatusList: [
    {
      value: "ALARMED",
      label: "报警中",
    },
    {
      value: "CLOSED",
      label: "已关闭",
    },
  ],
  contractSatus: {
    WAIT_SIGN: "待签署",
    SIGNED: "已签署",
  },
  contractManageType: {
    OP_MASTER: "主合同",
    OP_AUTHORITY: "服务区域授权补充协议合同",
    OP_NEW_MASTER: "新政策主合同",
  },
  opLevelType:{
    PROVINCIAL_LEVEL: "省级运维商",
    REGIONAL_LEVEL: "区域级运维商",
    TOWNSHIP_LEVEL: "乡镇级运维商",
  },
  isWarranty:{
    0:"是",
    1:"否"
  },
  auditStatus: {
    WAIT_AUDIT: "待审核",
    AUDIT_PASS: "审核通过",
    AUDIT_REJECT: "审核驳回",
},
};
