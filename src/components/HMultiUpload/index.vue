<template>
  <div>
    <el-upload
      ref="multiUploadRef"
      class="multi-avatar-uploader"
      v-bind="computedOptions"
      :file-list="fileList"
      :action="uploadAction"
      :headers="uploadHeaders"
      :disabled="isUploading || computedOptions.disabled"
      :before-upload="handleBeforeUpload"
      :on-remove="handleRemove"
      :on-preview="handlePictureCardPreview"
      :on-exceed="handleExceed"
      :on-success="handleSuccess"
      :on-error="handleError"
      :on-change="handleChange"
    >
      <div v-loading="isUploading">
        <el-icon v-if="computedOptions['list-type'] === 'picture-card' && fileList.length < computedOptions.limit && !computedOptions.disabled"><Plus /></el-icon>
        <div v-else-if="computedOptions['list-type'] !== 'picture-card'"
             class="custom-non-picture-card-trigger"
             :class="{ 'is-disabled': computedOptions.disabled || isUploading }"
             v-loading="isUploading"
             element-loading-text="上传中..."
        >
          <el-icon :size="50"><UploadFilled /></el-icon>
          <div class="el-upload__text">点击或将文件拖拽到这里上传</div>
        </div>
      </div>

      <template #file="{ file }">
        <div class="custom-upload-list-item" :class="{ 'is-uploading-custom': file.status === 'uploading' }">
          <img
            v-if="isImageFile(file) && file.url"
            class="el-upload-list__item-thumbnail"
            :src="file.url"
            alt=""
          />
          <div v-else-if="isVideoFile(file) && file.url" class="file-icon-container">
            <el-icon class="file-type-icon"><VideoPlay /></el-icon>
            <img v-if="file.thumbUrl" :src="file.thumbUrl" class="el-upload-list__item-thumbnail video-thumbnail-placeholder" alt="video thumbnail"/>
            <span v-else-if="file.name" class="file-name-ellipsis">{{ file.name }}</span>
          </div>
          <div v-else-if="isPdfFile(file)" class="file-icon-container">
            <el-icon class="file-type-icon"><Document /></el-icon>
            <span v-if="file.name" class="file-name-ellipsis">{{ file.name }}</span>
          </div>
          <div v-else class="file-icon-container">
            <el-icon class="file-type-icon"><Download /></el-icon>
            <span v-if="file.name" class="file-name-ellipsis">{{ file.name }}</span>
          </div>

          <span class="el-upload-list__item-status-label" v-if="file.status === 'uploading' || file.status === 'ready'">
            <el-progress
              v-if="file.status === 'uploading'"
              type="circle"
              :percentage="Number(file.percentage) || 0"
              :width="computedOptions['list-type'] === 'picture-card' ? 70 : 30"
              status="success"
            />
          </span>

          <div class="el-upload-list__item-actions custom-file-actions">
            <span
              class="action-icon"
              @click="handlePictureCardPreview(file)"
            >
              <el-icon><View /></el-icon>
            </span>
            <span
              v-if="!computedOptions.disabled"
              class="action-icon"
              @click="confirmThenRemove(file)"
            >
              <el-icon><Delete /></el-icon>
            </span>
          </div>
        </div>
      </template>

      <template #tip v-if="uploadTip || computedOptions.tip">
        <div class="el-upload__tip">
          {{ uploadTip || computedOptions.tip }}
        </div>
      </template>
    </el-upload>
    <el-dialog v-model="previewVisible" title="文件预览" append-to-body width="60%">
      <template v-if="previewImageUrl">
        <img
          v-if="isImageFile({ url: previewImageUrl })"
          :src="previewImageUrl"
          alt="Preview Image"
          style="width: 100%; max-height: 70vh; object-fit: contain;"
        />
        <video
          v-else-if="isVideoFile({ url: previewImageUrl })"
          :src="previewImageUrl"
          controls
          autoplay
          style="width: 100%; max-height: 70vh; object-fit: contain;"
        >
          您的浏览器不支持视频标签。
        </video>
        <div v-else style="text-align: center; padding: 20px;">
          <p>不支持此文件类型的直接预览。</p>
          <el-button type="primary" @click="() => window.open(previewImageUrl, '_blank')">尝试打开或下载</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ElMessage, ElMessageBox } from "element-plus";
import { computed, reactive, ref, watch } from "vue";
import { UploadClass } from '@/utils/upload';
import { Plus, View, Delete, Document, VideoPlay, Download, UploadFilled } from '@element-plus/icons-vue';

const emits = defineEmits([
  "update:modelValue",
  "fileChange",
  "fileError",
  "fileExceed",
]);

const props = defineProps({
  options: {
    type: Object,
    default: () => ({}),
  },
  disabled: {
    type: Boolean,
    default: false,
  },
  modelValue: {
    type: [Array, String],
    default: () => [],
  },
  uploadTip: {
    type: String,
    default: "",
  },
  limit: {
    type: Number,
    default: 3,
  },
  listType: {
    type: String,
    default: 'picture-card',
  },
  accept: {
    type: String,
    default: "image/*",
  },
  maxSizeMb: {
    type: Number,
    default: 10,
  }
});

const multiUploadRef = ref();
const previewVisible = ref(false);
const previewImageUrl = ref("");
const isUploading = ref(false);
const fileList = ref([]);

const uploadAction = UploadClass.action;
const uploadHeaders = reactive({});
try {
  const loginsStorage = window.localStorage.getItem("logins");
  if (loginsStorage) {
    const parsedLogins = JSON.parse(loginsStorage);
    if (parsedLogins && parsedLogins.access_token) {
      uploadHeaders.Authorization = `Bearer ${parsedLogins.access_token}`;
    }
  }
} catch (e) {
  console.error("HMultiUpload: Failed to parse logins from localStorage:", e);
}

const updateInternalFileList = (urls) => {
  let urlArray = [];
  if (typeof urls === 'string' && urls) {
    urlArray = urls.split(',');
  } else if (Array.isArray(urls)) {
    urlArray = urls;
  }

  fileList.value = urlArray.map((url, index) => ({
    name: `file-${index}.${url.split('.').pop() || 'file'}`,
    url: url,
    status: 'success',
    uid: Date.now() + index,
  }));
};

watch(() => props.modelValue, (newVal) => {
  const currentUrls = fileList.value.map(f => f.url).sort().join(',');
  const newUrls = (Array.isArray(newVal) ? newVal : (newVal ? newVal.split(',') : [])).sort().join(',');

  if (currentUrls !== newUrls) {
    updateInternalFileList(newVal);
  }
}, { immediate: true, deep: true });

const computedOptions = computed(() => {
  const defaultBaseOptions = {
    'list-type': props.listType,
    limit: props.limit,
    accept: props.accept,
    multiple: true,
    'show-file-list': true,
    name: "multipartFiles",
    autoUpload: true,
    disabled: props.disabled,
  };

  let fileTypeName = "文件";
  let accept = 'png,jpg,jpeg'
  if (props.accept) {
    const lowerAccept = props.accept.toLowerCase();
    if (lowerAccept.includes("image")) {
      fileTypeName = "图片";
      accept = 'png,jpg,jpeg';
    } else if (lowerAccept.includes("video")) {
      fileTypeName = "视频";
      accept = 'mp4'
    } else if (lowerAccept.includes("audio")) {
      fileTypeName = "音频";
    } else if (lowerAccept.includes("pdf")) {
      fileTypeName = "PDF文档";
      accept = 'pdf'
    } else if (lowerAccept.includes("zip") || lowerAccept.includes("rar")) {
      fileTypeName = "压缩包";
    } else if (lowerAccept.includes("doc") || lowerAccept.includes("word")) {
      fileTypeName = "Word文档";
    } else if (lowerAccept.includes("xls") || lowerAccept.includes("excel")) {
      fileTypeName = "Excel表格";
    } else if (lowerAccept.includes("ppt") || lowerAccept.includes("powerpoint")) {
      fileTypeName = "PPT";
    }
  }

  let defaultTip = `最多上传 ${props.limit} 个${fileTypeName}，单个不超过 ${props.maxSizeMb}MB。`;
  if (props.accept) {
    defaultTip += ` 支持格式：${accept}`;
  }

  const finalComputedOptions = {
    ...defaultBaseOptions,
    tip: defaultTip,
    ...props.options,
  };

  return finalComputedOptions;
});

const handleBeforeUpload = (rawFile) => {
  if (isUploading.value && computedOptions.value['list-type'] !== 'picture-card') {
      ElMessage.warning('请等待当前文件上传完成');
      return false;
  }

  const fileType = rawFile.type;
  const acceptedTypes = computedOptions.value.accept.split(',').map(t => t.trim());
  let isTypeValid = false;
  if (acceptedTypes.includes('image/*')) {
    isTypeValid = fileType.startsWith('image/');
  } else {
    isTypeValid = acceptedTypes.includes(fileType);
  }

  if (!isTypeValid) {
    ElMessage.error(`文件格式不正确! 请上传 ${computedOptions.value.accept} 格式的文件。`);
    return false;
  }
  const isSizeValid = rawFile.size / 1024 / 1024 < props.maxSizeMb;
  if (!isSizeValid) {
    ElMessage.error(`文件大小不能超过 ${props.maxSizeMb}MB!`);
    return false;
  }
  isUploading.value = true;
  return true;
};

const handleSuccess = (response, uploadFile, uploadFiles) => {
  isUploading.value = false;
  if (response && response.success && response.result && response.result[0]) {
    const newUrl = response.result[0]?.imageUrl || response.result[0]?.pdfUrl || response.result[0]?.fileUrl;
    if (newUrl) {
        uploadFile.url = newUrl;
    } else {
        ElMessage.error('上传成功，但未返回文件URL');
        const failedIndex = fileList.value.findIndex(f => f.uid === uploadFile.uid);
        if (failedIndex > -1) {
            fileList.value.splice(failedIndex, 1);
        }
        return;
    }
  } else {
    ElMessage.error(response.error || '文件上传失败');
    const failedIndex = fileList.value.findIndex(f => f.uid === uploadFile.uid);
    if (failedIndex > -1) {
        fileList.value.splice(failedIndex, 1);
    }
    return;
  }

  const newModelValue = fileList.value.map(f => f.url).filter(url => url);
  emits("update:modelValue", computedOptions.value['list-type'] === 'text' ? newModelValue.join(',') : newModelValue);
  emits("fileChange", { uploadFile, uploadFiles: fileList.value });
};

const handleError = (error, uploadFile, uploadFiles) => {
  isUploading.value = false;
  ElMessage.error('文件上传失败，请检查网络或联系管理员');
  emits("fileError", { error, uploadFile, uploadFiles });
  const index = fileList.value.findIndex(f => f.uid === uploadFile.uid);
  if (index !== -1) {
    fileList.value.splice(index, 1);
  }
  const newModelValueOnError = fileList.value.map(f => f.url).filter(url => url);
  emits("update:modelValue", computedOptions.value['list-type'] === 'text' ? newModelValueOnError.join(',') : newModelValueOnError);
};

const handleRemove = (uploadFile, uploadFiles) => {
  fileList.value = [...uploadFiles];
  const newModelValue = fileList.value.map(f => f.url).filter(url => url);
  emits("update:modelValue", computedOptions.value['list-type'] === 'text' ? newModelValue.join(',') : newModelValue);
  emits("fileChange", { uploadFile, uploadFiles: fileList.value });
};

const confirmThenRemove = async (file) => {
  if (computedOptions.value.disabled) return;
  try {
    await ElMessageBox.confirm(`确定移除 ${file.name} ?`, '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    });
    multiUploadRef.value?.handleRemove(file);
  } catch (e) {}
};

const getFileExtension = (fileNameOrUrl) => {
  if (!fileNameOrUrl || typeof fileNameOrUrl !== 'string') return '';
  return fileNameOrUrl.slice(((fileNameOrUrl.lastIndexOf(".") - 1) >>> 0) + 2).toLowerCase();
};

const isImageFile = (file) => {
  const ext = getFileExtension(file.name || file.url);
  const imageTypes = ['png', 'jpg', 'jpeg', 'gif', 'bmp', 'webp', 'svg'];
  if (imageTypes.includes(ext)) return true;
  if (file.raw && file.raw.type && file.raw.type.startsWith('image/')) return true;
  return false;
};

const isVideoFile = (file) => {
  const ext = getFileExtension(file.name || file.url);
  const videoTypes = ['mp4', 'webm', 'ogg', 'mov', 'avi', 'flv'];
  if (videoTypes.includes(ext)) return true;
  if (file.raw && file.raw.type && file.raw.type.startsWith('video/')) return true;
  return false;
};

const isPdfFile = (file) => {
  const ext = getFileExtension(file.name || file.url);
  if (ext === 'pdf') return true;
  if (file.raw && file.raw.type === 'application/pdf') return true;
  return false;
};

const handlePictureCardPreview = (uploadFile) => {
  const url = uploadFile.url;
  if (!url) {
    ElMessage.warning('文件URL不存在，无法预览');
    return;
  }

  if (isImageFile(uploadFile) || isVideoFile(uploadFile)) {
    previewImageUrl.value = url;
    previewVisible.value = true;
  } else if (isPdfFile(uploadFile)) {
    window.open(url, '_blank');
  } else {
    ElMessageBox.confirm('该文件类型可能无法直接预览，是否在新标签页尝试打开或下载？', '文件预览', {
      confirmButtonText: '打开/下载',
      cancelButtonText: '取消',
      type: 'warning',
    }).then(() => {
      window.open(url, '_blank');
    }).catch(() => {});
  }
};

const handleExceed = (files, uploadFiles) => {
  ElMessage.warning(
    `当前限制选择 ${computedOptions.value.limit} 个文件，本次选择了 ${files.length} 个文件，共选择了 ${
      files.length + uploadFiles.length
    } 个文件`
  );
  emits("fileExceed", { files, uploadFiles });
};

const handleChange = (uploadFile, uploadFiles) => {
    fileList.value = [...uploadFiles];
    emits("fileChange", { uploadFile, uploadFiles: fileList.value });
};

defineExpose({
  clearFiles: () => {
    multiUploadRef.value?.clearFiles();
    fileList.value = [];
    emits("update:modelValue", []);
  },
  abort: (file) => {
    multiUploadRef.value?.abort(file);
  },
  submit: () => {
    multiUploadRef.value?.submit();
  }
});

</script>

<style scoped lang="less">
.multi-avatar-uploader .el-upload {
  border: 1px dashed var(--el-border-color);
  border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
  transition: var(--el-transition-duration-fast);
}

.multi-avatar-uploader .el-upload:hover {
  border-color: var(--el-color-primary);
}

.el-icon.avatar-uploader-icon {
  font-size: 28px;
  color: #8c939d;
  width: 100%;
  height: 100%;
  text-align: center;
}

.custom-non-picture-card-trigger {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 20px;
  border: 1px dashed var(--el-border-color);
  border-radius: 6px;
  cursor: pointer;
  min-height: 100px;
  transition: border-color var(--el-transition-duration-fast);
}
.custom-non-picture-card-trigger:hover {
  border-color: var(--el-color-primary);
}
.custom-non-picture-card-trigger .el-upload__text {
  color: var(--el-text-color-regular);
  font-size: 14px;
  margin-top: 8px;
}
.custom-non-picture-card-trigger.is-disabled {
  cursor: not-allowed;
  background-color: var(--el-disabled-bg-color);
  border-color: var(--el-disabled-border-color);
}
.custom-non-picture-card-trigger.is-disabled .el-icon,
.custom-non-picture-card-trigger.is-disabled .el-upload__text {
  color: var(--el-text-color-disabled);
}

:deep(.el-upload--picture-card) {
  width: 100px;
  height: 100px;
  display: flex;
  align-items: center;
  justify-content: center;
}
:deep(.el-upload-list--picture-card .el-upload-list__item) {
  width: 100px;
  height: 100px;
}

:deep(.is-disabled.el-upload--picture-card) {
  display: none !important;
}

:deep(.el-upload-list--picture-card .el-upload-list__item) {
  position: relative;
}

:deep(.el-upload-list--picture-card .el-upload-list__item-actions) {
  position: absolute;
  width: 100%;
  height: 100%;
  left: 0;
  top: 0;
  cursor: default;
  display: flex;
  justify-content: center;
  align-items: center;
  color: #fff;
  opacity: 0;
  font-size: 20px;
  background-color: rgba(0, 0, 0, 0.5);
  transition: opacity var(--el-transition-duration-fast);
  z-index: 10;
}

:deep(.el-upload-list--picture-card .el-upload-list__item:hover .el-upload-list__item-actions) {
  opacity: 1;
}

.custom-file-actions .action-icon {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  padding: 0 5px;
}

.custom-file-actions .el-icon {
  font-size: 22px;
  color: #fff;
}

.custom-file-actions .action-icon + .action-icon {
  margin-left: 10px;
}

.custom-upload-list-item {
  width: 100%;
  height: 100%;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
}

.el-upload-list__item-thumbnail {
  width: 100%;
  height: 100%;
  object-fit: cover;
}
.video-thumbnail-placeholder {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.file-icon-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
  background-color: #f0f2f5;
}

.file-type-icon {
  font-size: 30px;
  color: #8c939d;
  margin-bottom: 5px;
}
.file-name-ellipsis {
  font-size: 12px;
  color: #606266;
  padding: 0 5px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  width: 90%;
  text-align: center;
}

.custom-upload-list-item:hover .custom-file-actions {
  opacity: 1;
}

.custom-file-actions {
  position: absolute;
  width: 100%;
  height: 100%;
  left: 0;
  top: 0;
  cursor: default;
  display: flex;
  justify-content: center;
  align-items: center;
  color: #fff;
  opacity: 0;
  font-size: 20px;
  background-color: rgba(0, 0, 0, 0.5);
  transition: opacity var(--el-transition-duration-fast);
  z-index: 10;
}

:deep(.el-upload-list--picture-card .el-upload-list__item .el-icon--check) {
   display: none;
}

:deep(.el-upload-list__item.is-uploading .el-progress__text) {
  color: #fff !important;
}
:deep(.el-upload-list__item.is-uploading circle) {
  stroke: #fff !important;
}

.custom-upload-list-item.is-uploading-custom {
  & > .el-upload-list__item-thumbnail,
  & > .file-icon-container {
    filter: brightness(0.7);
  }
}

.el-upload__tip {
  font-size: 12px;
  color: #999;
  margin-top: 7px;
}
</style>
