<script setup lang="ts">
import { onMounted, ref, useAttrs, watch } from 'vue';

import { watchOnce } from '@vueuse/core';
import API from '@/api/maintainance';
import Quill from 'quill';
import QuillResizeImage from 'quill-resize-image';

const Size = Quill.import('attributors/style/size') as any;
Size.whitelist = ['12px', '14px', '16px', '18px'];
Quill.register(Size, true);
Quill.register("modules/resize", QuillResizeImage);

const props = defineProps({
  disabled: {
    type: Boolean,
    default: false,
  },
  placeholder: {
    type: String,
    default: '请输入',
  },
});


const attrs = useAttrs();

const editorRef = ref<HTMLElement>();
type QuillSource = (typeof Quill.sources)[keyof typeof Quill.sources];

let textChangeSource: QuillSource = Quill.sources.SILENT;
let quill: Quill;

const data = defineModel<string>({ default: null });

const FONT_SIZES = [false, '12px', '14px', '16px', '18px'];

watch(
  () => props.disabled,
  (newval) => {
    setTimeout(() => {
      if (newval) {
        quill && quill.disable();
        quill && (quill.root.dataset.placeholder = props.placeholder);
      } else {
        quill && quill.enable();
        quill &&
          (quill.root.dataset.placeholder =
            quill.options.placeholder || '请输入');
      }
    }, 0);
  },
  {
    immediate: true,
  },
);

watchOnce(data, () => {
  setTimeout(() => {
    if (textChangeSource !== Quill.sources.SILENT) {
      return;
    }
   
    quill.clipboard.dangerouslyPasteHTML(
      data.value || '',
      Quill.sources.SILENT,
    );
  }, 0);
});

onMounted(() => {
  quill = new Quill(editorRef.value!, {
    theme: 'snow',
    modules: {
      toolbar: [
          ['bold', 'italic', 'underline', 'strike'],
          ['image'],
          [{ 'list': 'ordered'}, { 'list': 'bullet' }, { 'list': 'check' }],
          [{ 'indent': '-1'}, { 'indent': '+1' }],

          [{ 'size': FONT_SIZES }],
          [{ 'header': [1, 2, 3, 4, 5, 6, false] }],

          [{ 'color': [] }, { 'background': [] }],
          [{ 'font': [] }],
          [{ 'align': [] }],
      ],
      resize: {
        locale: {
          center: "center",
        },
      },
      syntax: false,
    },
    placeholder: props.placeholder,
  });
  const toolbar = quill.getModule('toolbar') as any;
  toolbar.addHandler('image', () => {
    const input = document.createElement('input');
    input.setAttribute('type', 'file');
    input.setAttribute('accept', 'image/*');
    input.click();
    input.onchange = async () => {
      const file = input.files![0];
      const formData = new FormData();
      formData.append('multipartFiles', file);
      const res = await API.uploadImage(formData);
      console.log(res)
      if (res?.data?.result?.[0]?.fileUrl) {
        const range = quill.getSelection(true);
        console.log(res.data.result[0].fileUrl)
        quill.insertEmbed(range.index, 'image', res.data.result[0].fileUrl);
      }
    };
  });

  quill.on('text-change', (_newDelta, _oldDelta, source) => {
    if (source === Quill.sources.SILENT) {
      return;
    }
    textChangeSource = source;
    data.value =  quill.getSemanticHTML();
  });

  quill.on('composition-start', () => {
    quill.root.dataset.placeholder = '';
  });

  quill.on('composition-end', () => {
    quill.root.dataset.placeholder = quill.options.placeholder;
  });
});

const handleClick = (evt: any) => {
  if (evt.target.className !== 'ql-image' && evt.target.tagName !== 'SVG') {
    evt.preventDefault();
  }
};

const clearContent = () => {
  quill.setContents([]);
}

defineExpose({
  clearContent
})
</script>
<template>
  <div
    v-bind="attrs"
    :class="[props.disabled ? 'is-disabled' : '']"
    class="editor"
    @click="handleClick"
  >
    <div ref="editorRef"></div>
  </div>
</template>

<style lang="less" scoped>
.editor {
  --el-color-primary-light-9: rgb(2 93 255 / 15%);
  --el-color-primary-light-8: var(--el-color-primary);
  --border: var(--el-border-color);

  display: flex;
  flex-direction: column;
  width: 100%;
  color: var(--el-text-color-regular);
  min-height: 100px;

  &.is-disabled {
    :deep(.ql-editor) {
      cursor: not-allowed;
      background-color: var(--el-disabled-bg-color);

      & > * {
        cursor: not-allowed;
      }
    }
  }

  

  :deep(.ql-toolbar) {
    border-color: var(--el-border-color) !important;

    .ql-picker-label {
      display: flex;
    }
  }

  :deep(.ql-container) {
    border-color: var(--el-border-color) !important;
    min-height: 200px;
  }

  :deep(.ql-editor::before) {
    font-style: normal;
    color: var(--el-text-color-placeholder);
  }
}
</style>

<style>
.ql-snow .ql-tooltip[data-mode='link']::before {
  content: '请输入链接地址:' !important;
}

.ql-snow .ql-tooltip.ql-editing a.ql-action::after {
  padding-right: 0;
  content: '保存';
  border-right: 0;
}

.ql-snow .ql-tooltip[data-mode='video']::before {
  content: '请输入视频地址:' !important;
}

.ql-snow .ql-picker.ql-size .ql-picker-label::before,
.ql-snow .ql-picker.ql-size .ql-picker-item::before {
  content: '默认' !important;
}

.ql-snow .ql-picker.ql-size .ql-picker-label[data-value='12px']::before,
.ql-snow .ql-picker.ql-size .ql-picker-item[data-value='12px']::before {
  content: '12px' !important;
}

.ql-snow .ql-picker.ql-size .ql-picker-label[data-value='14px']::before,
.ql-snow .ql-picker.ql-size .ql-picker-item[data-value='14px']::before {
  content: '14px' !important;
}

.ql-snow .ql-picker.ql-size .ql-picker-label[data-value='16px']::before,
.ql-snow .ql-picker.ql-size .ql-picker-item[data-value='16px']::before {
  content: '16px' !important;
}

.ql-snow .ql-picker.ql-size .ql-picker-label[data-value='18px']::before,
.ql-snow .ql-picker.ql-size .ql-picker-item[data-value='18px']::before {
  content: '18px' !important;
}

.ql-snow .ql-picker.ql-header .ql-picker-label::before,
.ql-snow .ql-picker.ql-header .ql-picker-item::before {
  content: '文本' !important;
}

.ql-snow .ql-picker.ql-header .ql-picker-label[data-value='1']::before,
.ql-snow .ql-picker.ql-header .ql-picker-item[data-value='1']::before {
  content: '标题1' !important;
}

.ql-snow .ql-picker.ql-header .ql-picker-label[data-value='2']::before,
.ql-snow .ql-picker.ql-header .ql-picker-item[data-value='2']::before {
  content: '标题2' !important;
}

.ql-snow .ql-picker.ql-header .ql-picker-label[data-value='3']::before,
.ql-snow .ql-picker.ql-header .ql-picker-item[data-value='3']::before {
  content: '标题3' !important;
}

.ql-snow .ql-picker.ql-header .ql-picker-label[data-value='4']::before,
.ql-snow .ql-picker.ql-header .ql-picker-item[data-value='4']::before {
  content: '标题4' !important;
}

.ql-snow .ql-picker.ql-header .ql-picker-label[data-value='5']::before,
.ql-snow .ql-picker.ql-header .ql-picker-item[data-value='5']::before {
  content: '标题5' !important;
}

.ql-snow .ql-picker.ql-header .ql-picker-label[data-value='6']::before,
.ql-snow .ql-picker.ql-header .ql-picker-item[data-value='6']::before {
  content: '标题6' !important;
}

.ql-snow .ql-picker.ql-font .ql-picker-label[data-value='SimSun']::before,
.ql-snow .ql-picker.ql-font .ql-picker-item[data-value='SimSun']::before {
  content: '宋体' !important;
}

.ql-font-SimSun {
  font-family: SimSun;
}

.ql-snow .ql-picker.ql-font .ql-picker-label[data-value='SimHei']::before,
.ql-snow .ql-picker.ql-font .ql-picker-item[data-value='SimHei']::before {
  content: '黑体' !important;
}

.ql-font-SimHei {
  font-family: SimHei !important;
}

.ql-snow
  .ql-picker.ql-font
  .ql-picker-label[data-value='Microsoft-YaHei']::before,
.ql-snow
  .ql-picker.ql-font
  .ql-picker-item[data-value='Microsoft-YaHei']::before {
  content: '微软雅黑' !important;
}

.ql-font-Microsoft-YaHei {
  font-family: 'Microsoft YaHei';
}

.ql-snow .ql-picker.ql-font .ql-picker-label[data-value='KaiTi']::before,
.ql-snow .ql-picker.ql-font .ql-picker-item[data-value='KaiTi']::before {
  content: '楷体' !important;
}

.ql-font-KaiTi {
  font-family: KaiTi;
}

.ql-snow .ql-picker.ql-font .ql-picker-label[data-value='FangSong']::before,
.ql-snow .ql-picker.ql-font .ql-picker-item[data-value='FangSong']::before {
  content: '仿宋' !important;
}

.ql-font-FangSong {
  font-family: FangSong;
}
</style>
