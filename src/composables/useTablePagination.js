import { ref, reactive, computed } from 'vue';
import _ from 'lodash';
import { ElMessage } from 'element-plus';

/**
 * 表格分页逻辑组合函数
 * @param {Function} apiFn - 获取列表数据的 API 函数，应返回 Promise，格式需符合项目规范
 * @param {Object|Function} initialParams - 初始查询参数或返回初始查询参数的函数
 * @param {Object} options - 可选配置
 * @param {boolean} options.isPost - API 是否为 POST 请求 (影响响应数据结构处理)，默认为 false (GET)
 * @param {boolean} options.manual - 是否手动触发首次加载，默认为 false (自动加载)
 * @param {Function} options.formatResult - 格式化 API 返回结果的函数
 * @param {Function} options.onError - API 请求失败时的回调
 */
export function useTablePagination(apiFn, initialParams = {}, options = {}) {
  const { isPost = false, manual = false, formatResult, onError, pageSize = 10 } = options;

  // 分页和加载状态
  const loading = ref(false);
  const listArr = ref([]);
  const total = ref(0);
  const pagination = reactive({
    pageNum: 1,
    pageSize,
  });

  const getQueryParams = () => {
    const baseParams = typeof initialParams === 'function' ? initialParams() : initialParams;
    const clonedParams = _.cloneDeep(baseParams);
    return { ...clonedParams, ...pagination };
  };


  // 获取列表数据
  const getList = async () => {
    loading.value = true;
    try {
      const params = getQueryParams();
      const response = await apiFn(params);
      let resultData;
      let success;
      let errorMsg;

      // 根据请求类型处理响应结构
      if (isPost) {
        if (response.data && response.data.success) {
          resultData = response.data.result;
          success = true;
        } else {
          success = false;
          errorMsg = response.data?.error || response.data?.message || '获取列表失败';
        }
      } else {
        if (response && response.success) {
          resultData = response.result;
          success = true;
        } else {
          success = false;
          errorMsg = response?.error || response?.message || '获取列表失败';
        }
      }

      if (success) {
        // 检查分页数据结构
        if (resultData && typeof resultData.totalElements !== 'undefined' && Array.isArray(resultData.content)) {
          listArr.value = formatResult ? formatResult(resultData.content) : resultData.content;
          total.value = resultData.totalElements;
        } else {
          // 兼容非标准分页或列表接口
          listArr.value = formatResult ? formatResult(resultData) : (Array.isArray(resultData) ? resultData : []);
          total.value = listArr.value.length; // Fallback total
        }
      } else {
        listArr.value = [];
        total.value = 0;
        ElMessage.error(errorMsg);
        if (onError) onError(errorMsg);
      }
    } catch (error) {
      listArr.value = [];
      total.value = 0;
      const message = error.message || '请求失败';
      ElMessage.error(message);
      if (onError) onError(message, error);
      console.error('API Request Error:', error); // 打印详细错误
    } finally {
      loading.value = false;
    }
  };

  // 查询列表（重置页码）
  const queryList = () => {
    pagination.pageNum = 1;
    getList();
  };

  // 改变每页数量
  const changeSize = (size) => {
    pagination.pageSize = size;
    pagination.pageNum = 1;
    getList();
  };

  // 改变当前页码
  const changeCurrent = (curr) => {
    pagination.pageNum = curr;
    getList();
  };

  // 如果不是手动加载，则在初始化时加载数据
  if (!manual) {
    // getList(); // 延迟到外部 onMounted 调用，避免 setup 期间过早请求
  }

  return {
    loading,
    listArr,
    total,
    pagination,
    getList,
    queryList,
    changeSize,
    changeCurrent,
  };
}
