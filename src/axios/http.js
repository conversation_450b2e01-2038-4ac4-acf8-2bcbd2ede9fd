import axios from "axios";
import qs from "qs";
import {
	ElMessage
} from 'element-plus'
import router from "../router";

const base = import.meta.env.VITE_APP_BASE_API;
const ciphertext = import.meta.env.VITE_APP_BASE_AUTH;

// 默认配置
axios.defaults.timeout = 1500000;
axios.defaults.baseURL = base;

// 请求拦截器
axios.interceptors.request.use(
	config => {
		// At：2023-11-21
		// 为处理电费收益报表导出接口的数组传参进行单独判断
		if (config.method === 'get' && config.url ==="/hdsapi/report/stationElecIncome/doExcel" ) {
			config.paramsSerializer = function (params) {
				return qs.stringify(params, { arrayFormat: 'repeat' })
			}
		}
		const token = JSON.parse(window.localStorage.getItem("logins"))?.access_token;
		config.headers.Authorization = `Bearer ${token}`
		if (config.url === "/oauth2/oauth/token") {
			config.headers.Authorization = ciphertext;
		}
		return Promise.resolve(config);
	},
	error => {
		return Promise.reject(error);
	}
);

// 响应拦截器
axios.interceptors.response.use(
	response => {
		const errCode = response.data.error
		errCode && resHandle(errCode)
		return Promise.resolve(response);
	},
	error => {
		errHandle(error)
		return Promise.reject(error);
	}
);

// 请求成功返回的异常处理
const resHandle = (errCode) => {
	ElMessage.closeAll()
	switch (errCode) {
		case "invalid_token":
			ElMessage.error("登录超时，请重新登录");
			window.localStorage.clear();
			router.push("/login")
			break;
	}
}

// 一般请求的异常处理，其它在自己页面自定义
const errHandle = (err) => {
	ElMessage.closeAll()
	if (err.response) {
		switch (err.response.status) {
			// case 400: ElMessage.error("请求错误（400）");  // 登录单独处理
			//     break
			// case 401:
			// 	ElMessage.error("未授权，请重新登录（401）");
			// 	break;
			case 403:
				ElMessage.error("拒绝访问（403）");
				break;
			case 404:
				ElMessage.error("请求出错（404）");
				break;
			case 408:
				ElMessage.error("请求超时（408）");
				break;
			case 500:
				ElMessage.error("服务器错误（500）");
				break;
			case 501:
				ElMessage.error("服务未实现（501）");
				break;
			case 502:
				ElMessage.error("网络错误（502）");
				break;
			case 503:
				ElMessage.error("服务不可用（503）");
				break;
			case 504:
				ElMessage.error("网络超时（504）");
				break;
			case 505:
				ElMessage.error("HTTP版本不受支持（505）");
				break;
			case 0:
				ElMessage.error("连接服务器失败");
				break;
		}
	} else {
		ElMessage.error("连接服务器失败");
	}
}


/**
 * 封装get方法，对应get请求
 * @param {String} url [请求的url地址]
 * @param {Object} params [请求时携带的参数]
 */
const get = (url, params, sign) => {
	if (sign === 3) {
		axios.defaults.responseType = "blob";
		axios.defaults.headers["Content-Type"] = "application/x-download;charset=utf-8";
	} else {
		axios.defaults.responseType = "";
		axios.defaults.headers["Content-Type"] = "application/json";
	}
	return new Promise((resolve, reject) => {
		axios.get(url, {
				params
			})
			.then(res => {
				resolve(res.data);
			})
			.catch(err => {
				// ElMessage({
				// 	type: 'error',
				// 	message: err.response ? err.response?.data?.message : err.message,
				// })
				reject(err);
			});
	});
}

/**
 * post方法，对应post请求
 * @param {String} url [请求的url地址]
 * @param {Object} params [请求时携带的参数]
 */
const post = (url, params, sign) => {
	return new Promise((resolve, reject) => {
		axios.defaults.responseType = "";
		if (sign === 1) {
			axios.defaults.headers["Content-Type"] = "application/json";
		} else if (sign === 3) { // 导出
			axios.defaults.responseType = "blob";
			axios.defaults.headers["Content-Type"] = "application/json";
		} else if (sign === 5) { // 上传
			axios.defaults.headers["Content-Type"] = "multipart/form-data";
		} else {
			axios.defaults.headers["Content-Type"] = "application/x-www-form-urlencoded;charset=utf-8";
		}
		axios.post(url, (sign === 1 || sign === 3 || sign === 5) ? params : qs.stringify(params))
			.then(res => {
				resolve(res);
			})
			.catch(err => {
				// ElMessage({
				// 	type: 'error',
				// 	message: err.response ? err.response?.data?.message : err.message,
				// })
				reject(err);
			});
	});
}

/**
 * put方法，对应put请求
 * @param {String} url [请求的url地址]
 * @param {Object} params [请求时携带的参数]
 */
const put = (url, params, sign) => {
	return new Promise((resolve, reject) => {
		if (sign !== 1) {
			axios.defaults.headers["Content-Type"] = "application/json";
		}else {
			axios.defaults.headers["Content-Type"] = "application/x-www-form-urlencoded;charset=utf-8";
		}
		axios.put(url, sign !== 1 ? params : qs.stringify(params))
			.then(res => {
				resolve(res);
			})
			.catch(err => {
				reject(err);
			});
	});
}
/**
 * put方法，对应put请求
 * @param {String} url [请求的url地址]
 * @param {Object} params [请求时携带的参数]
 */
const del = (url, params) => {
	return new Promise((resolve, reject) => {
		axios.delete(url, params)
			.then(res => {
				resolve(res);
			})
			.catch(err => {
				reject(err);
			});
	});
}

export {
	del,
	put,
	get,
	post
};
